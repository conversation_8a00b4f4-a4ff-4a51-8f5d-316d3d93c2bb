#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析个股人气表删除逻辑和数据流程
"""
import psycopg2

def analyze_deletion_logic():
    # 连接数据库
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'tradefusion',
        'user': 'postgres',
        'password': 'ymjatTUU520'
    }
    
    try:
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        
        # 查询最新日期
        cursor.execute('SELECT MAX("日期") FROM "个股人气表"')
        max_date = cursor.fetchone()[0]
        print(f'=== 个股人气表数据分析 (日期: {max_date}) ===\n')
        
        if not max_date:
            print("❌ 数据库中没有任何日期数据")
            return
        
        # 1. 总体数据统计
        cursor.execute('SELECT COUNT(*) FROM "个股人气表" WHERE "日期" = %s', (max_date,))
        total_count = cursor.fetchone()[0]
        print(f'📊 总记录数: {total_count}')
        
        # 2. 数据源分布
        cursor.execute('SELECT COUNT(*) FROM "个股人气表" WHERE "日期" = %s AND "东财人气排名" IS NOT NULL', (max_date,))
        dcf_count = cursor.fetchone()[0]
        print(f'📈 有东财排名的记录数: {dcf_count}')
        
        cursor.execute('SELECT COUNT(*) FROM "个股人气表" WHERE "日期" = %s AND "同花人气排名" IS NOT NULL', (max_date,))
        ths_count = cursor.fetchone()[0]
        print(f'📈 有同花排名的记录数: {ths_count}')
        
        cursor.execute('''
            SELECT COUNT(*) FROM "个股人气表" 
            WHERE "日期" = %s 
            AND "东财人气排名" IS NOT NULL 
            AND "同花人气排名" IS NOT NULL
        ''', (max_date,))
        both_count = cursor.fetchone()[0]
        print(f'📈 同时有两个数据源的记录数: {both_count}')
        
        # 3. 综合人气评分分析
        cursor.execute('''
            SELECT "综合人气评分", COUNT(*) 
            FROM "个股人气表" 
            WHERE "日期" = %s 
            GROUP BY "综合人气评分" 
            ORDER BY "综合人气评分"
        ''', (max_date,))
        score_distribution = cursor.fetchall()
        print(f'\n📊 综合人气评分分布:')
        positive_count = 0
        for score, count in score_distribution:
            if score > 0:
                positive_count += count
            print(f'   评分 {score}: {count} 条记录')
        
        print(f'✅ 综合人气评分 > 0 的记录数: {positive_count}')
        
        # 4. 分析删除逻辑的影响
        print(f'\n🔍 删除逻辑分析:')
        print(f'   删除条件: 综合人气评分 = 0 OR 综合人气评分 IS NULL')
        
        zero_or_null_count = total_count - positive_count
        print(f'   符合删除条件的记录数: {zero_or_null_count}')
        
        # 5. 综合评分计算条件分析
        print(f'\n🧮 综合评分计算条件分析:')
        print(f'   计算条件: 东财排名 1-99 AND 同花排名 1-99 AND 两个评分都>10')
        
        # 检查不满足条件的情况
        cursor.execute('''
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN "东财人气排名" IS NULL THEN 1 ELSE 0 END) as no_dcf,
                SUM(CASE WHEN "同花人气排名" IS NULL THEN 1 ELSE 0 END) as no_ths,
                SUM(CASE WHEN "东财人气排名" IS NOT NULL AND "同花人气排名" IS NOT NULL 
                         AND NOT ("东财人气排名" BETWEEN 1 AND 99) THEN 1 ELSE 0 END) as dcf_out_range,
                SUM(CASE WHEN "东财人气排名" IS NOT NULL AND "同花人气排名" IS NOT NULL 
                         AND NOT ("同花人气排名" BETWEEN 1 AND 99) THEN 1 ELSE 0 END) as ths_out_range,
                SUM(CASE WHEN "东财人气排名" BETWEEN 1 AND 99 AND "同花人气排名" BETWEEN 1 AND 99
                         AND ((100 - "东财人气排名" + 1) <= 10) THEN 1 ELSE 0 END) as dcf_score_low,
                SUM(CASE WHEN "东财人气排名" BETWEEN 1 AND 99 AND "同花人气排名" BETWEEN 1 AND 99
                         AND ((100 - "同花人气排名" + 1) <= 10) THEN 1 ELSE 0 END) as ths_score_low
            FROM "个股人气表" 
            WHERE "日期" = %s AND "综合人气评分" = 0
        ''', (max_date,))
        
        result = cursor.fetchone()
        if result:
            total, no_dcf, no_ths, dcf_out_range, ths_out_range, dcf_score_low, ths_score_low = result
            print(f'   评分为0的记录分析 (总计 {total} 条):')
            print(f'     - 缺少东财数据: {no_dcf} 条')
            print(f'     - 缺少同花数据: {no_ths} 条')
            print(f'     - 东财排名超出1-99范围: {dcf_out_range} 条')
            print(f'     - 同花排名超出1-99范围: {ths_out_range} 条')
            print(f'     - 东财评分≤10: {dcf_score_low} 条')
            print(f'     - 同花评分≤10: {ths_score_low} 条')
        
        # 6. 数据质量评估
        print(f'\n📈 数据质量评估:')
        if both_count > 0:
            quality_rate = positive_count / both_count * 100
            print(f'   双数据源记录中有效评分比例: {quality_rate:.1f}% ({positive_count}/{both_count})')
        
        if total_count > 0:
            retention_rate = positive_count / total_count * 100
            print(f'   总记录保留率: {retention_rate:.1f}% ({positive_count}/{total_count})')
            deletion_rate = (total_count - positive_count) / total_count * 100
            print(f'   预期删除率: {deletion_rate:.1f}% ({total_count - positive_count}/{total_count})')
        
        # 7. 具体的排名分布分析
        print(f'\n📊 排名分布分析:')
        if dcf_count > 0:
            cursor.execute('''
                SELECT MIN("东财人气排名"), MAX("东财人气排名"), AVG("东财人气排名")
                FROM "个股人气表" 
                WHERE "日期" = %s AND "东财人气排名" IS NOT NULL
            ''', (max_date,))
            dcf_stats = cursor.fetchone()
            print(f'   东财排名范围: {dcf_stats[0]} - {dcf_stats[1]}, 平均: {dcf_stats[2]:.1f}')
        
        if ths_count > 0:
            cursor.execute('''
                SELECT MIN("同花人气排名"), MAX("同花人气排名"), AVG("同花人气排名")
                FROM "个股人气表" 
                WHERE "日期" = %s AND "同花人气排名" IS NOT NULL
            ''', (max_date,))
            ths_stats = cursor.fetchone()
            print(f'   同花排名范围: {ths_stats[0]} - {ths_stats[1]}, 平均: {ths_stats[2]:.1f}')
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")

if __name__ == "__main__":
    analyze_deletion_logic()
