"""
个股接力表计算模块

功能说明：
1. 使用板块精选表（1号模块）获取高质量的当日热门板块
2. 结合板块涨停表（2号模块）获取历史板块数据
3. 计算持续热门板块，筛选相关股票
4. 生成个股接力评分

优化说明：
- 优先使用板块精选表数据（经过双重筛选，质量更高）
- 板块涨停表已优化为只保留前7名，无需额外限制
- 降级机制确保数据可用性和系统稳定性
"""

# {{ AURA-X: Modify - 将SQLite连接改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
import psycopg2
import psycopg2.extras
import os
import logging
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器
logger = 获取日志器("L5-个股接力表")

def get_stock_sectors_unified(cursor, stock_code, today, exclude_current_date=True):
    """
    统一的股票板块获取逻辑（基于最近6个日期）

    Args:
        cursor: 数据库游标
        stock_code: 股票代码
        today: 当前日期
        exclude_current_date: 是否排除当日

    Returns:
        set: 所属板块名称集合
    """
    try:
        # 获取最近6个日期
        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        if exclude_current_date:
            # 排除当日（用于Y支股票）
            cursor.execute('''
                SELECT DISTINCT "日期" FROM "个股板块关联表"
                WHERE "日期" < %s
                ORDER BY "日期" DESC
                LIMIT 6
            ''', (today,))
            date_desc = "最近6天（排除当日）"
        else:
            # 包含当日（用于X支股票）
            cursor.execute('''
                SELECT DISTINCT "日期" FROM "个股板块关联表"
                WHERE "日期" <= %s
                ORDER BY "日期" DESC
                LIMIT 6
            ''', (today,))
            date_desc = "最近6天（包含当日）"

        history_dates = [row[0] for row in cursor.fetchall()]

        if not history_dates:
            return set()

        # 获取板块关系
        date_placeholders = ','.join(['%s'] * len(history_dates))
        cursor.execute(f'''
            SELECT DISTINCT "所属板块名称"
            FROM "个股板块关联表"
            WHERE "股票代码" = %s AND "日期" IN ({date_placeholders})
        ''', [stock_code] + history_dates)

        sectors = set([row[0] for row in cursor.fetchall()])
        # logger.debug(f"股票 {stock_code} {date_desc}板块关系: {sectors}")
        return sectors

    except Exception as e:
        logger.记录错误(f"获取股票 {stock_code} 板块关系失败", e)
        return set()

def main():
    # {{ AURA-X: Modify - 修改为PostgreSQL连接配置. Approval: 寸止(ID:1737734400). }}
    # PostgreSQL连接配置
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'tradefusion',
        'user': 'postgres',
        'password': 'ymjatTUU520'
    }
    conn = None

    try:
        # 配置PostgreSQL数据库连接
        conn = psycopg2.connect(**db_config)
        conn.autocommit = False
        cursor = conn.cursor()

        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        # 步骤一：获取核心日期
        cursor.execute('SELECT MAX("日期") FROM "板块信息表"')
        today = cursor.fetchone()[0]
        if not today:
            logger.记录错误("未找到有效日期")
            print("未找到有效日期")
            return False
        # 移除调试日志
        print(f"当前处理日期：{today}")

        # 获取五天前的日期
        cursor.execute('''
            SELECT DISTINCT "日期" FROM "板块信息表"
            WHERE "日期" < %s
            ORDER BY "日期" DESC
            LIMIT 5
        ''', (today,))
        five_days = [row[0] for row in cursor.fetchall()]
        if len(five_days) < 5:
            logger.记录错误("五天前的日期不足")
            print("五天前的日期不足")
            return False
        # 移除调试日志
        print(f"历史五天日期：{five_days}")

        # 步骤二：直接使用板块精选表结果（1号模块已完成高质量筛选）
        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        cursor.execute('''
            SELECT "日期", "板块名称", "综合评分" FROM "板块精选表"
            WHERE "日期" = %s
            ORDER BY "综合评分" DESC
        ''', (today,))
        premium_sectors = cursor.fetchall()

        if not premium_sectors:
            logger.记录错误("今日无精选板块数据，1号模块可能未运行")
            print("今日无精选板块数据，1号模块可能未运行")
            return

        # 直接使用精选板块作为热门板块（已经过双重筛选：SMA前4 AND 最新前3）
        A = premium_sectors
        # 移除调试日志
        print(f"使用1号模块精选板块：{len(A)}个板块")

        for _, sector, score in A:
            # 移除调试日志
            print(f"  精选板块: {sector} (综合评分: {score})")

        # 步骤三：简化版 - 直接使用精选板块，无需重复计算历史交集
        # 1号模块已经基于5天历史数据计算了SMA和持续性
        # 这里直接将精选板块作为持续热门板块使用

        # 移除调试日志
        print("直接使用1号模块精选板块进行股票筛选")

        # 步骤三：处理X支股票（已涨停，基于最近6天板块关系）
        # 3.1 先获取属于精选板块的X支股票
        premium_sector_names = set([sector for _, sector, _ in A])
        a_params = [(date, sector) for (date, sector, _) in A]
        # {{ AURA-X: Modify - 修复PostgreSQL占位符语法. Approval: 寸止(ID:1737734400). }}
        placeholders = ','.join(['(%s, %s)'] * len(a_params))
        params = [item for pair in a_params for item in pair]

        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        cursor.execute(f'''
            SELECT DISTINCT g."股票代码"
            FROM "个股板块关联表" g
            INNER JOIN "个股人气表" r
                ON g."股票代码" = r."股票代码"
                AND r."日期" = %s
            WHERE (g."日期", g."所属板块名称") IN ({placeholders})
                AND r."综合人气评分" > 80
        ''', [today] + params)
        x_stock_codes = [row[0] for row in cursor.fetchall()]

        # 3.2 使用统一逻辑获取X支股票的所有板块关系（基于最近6天，包含当日）
        X_stocks = []
        x_stocks_with_sectors = 0
        if x_stock_codes:
            for stock_code in x_stock_codes:
                sectors = get_stock_sectors_unified(cursor, stock_code, today, exclude_current_date=False)
                # 验证是否属于精选板块（必须条件）
                premium_sectors_for_stock = sectors & premium_sector_names
                if premium_sectors_for_stock:  # 必须有精选板块
                    x_stocks_with_sectors += 1
                    # 包含所有板块（精选板块 + 辅助板块）
                    for sector in sectors:
                        X_stocks.append((stock_code, sector))
                    # 移除调试日志
                else:
                    # logger.warning(f"X支股票 {stock_code} 在最近6天（含当日）无精选板块关系，排除")
                    pass

        # 移除调试日志
        print(f"X支股票（已涨停）：{len(x_stock_codes)}只候选，{x_stocks_with_sectors}只有效，{len(X_stocks)}条板块关系（基于最近6天，含当日）")

        # 步骤四：处理Y支股票（未涨停，基于最近6天板块关系）
        # 4.1 获取Y支股票名单：人气>80且未涨停
        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        cursor.execute('''
            SELECT DISTINCT h."股票代码"
            FROM "个股人气表" h
            WHERE h."综合人气评分" > 80
              AND h."日期" = %s
              AND NOT EXISTS (
                  SELECT 1 FROM "个股板块关联表" g
                  WHERE g."股票代码" = h."股票代码" AND g."日期" = %s
              )
        ''', (today, today))
        y_stock_codes = [row[0] for row in cursor.fetchall()]
        # logger.info(f"Y支股票候选名单：{len(y_stock_codes)}只（人气>80且未涨停）")
        print(f"Y支股票候选名单：{len(y_stock_codes)}只（人气>80且未涨停）")

        # 4.2 使用统一逻辑获取Y支股票的板块关系（基于最近6天，排除当日）
        Y_stocks = []
        y_stocks_with_sectors = 0
        if y_stock_codes:
            for stock_code in y_stock_codes:
                sectors = get_stock_sectors_unified(cursor, stock_code, today, exclude_current_date=True)
                # 验证是否属于精选板块（必须条件）
                premium_sectors_for_stock = sectors & premium_sector_names
                if premium_sectors_for_stock:  # 必须有精选板块
                    y_stocks_with_sectors += 1
                    # 包含所有板块（精选板块 + 辅助板块）
                    for sector in sectors:
                        Y_stocks.append((stock_code, sector))
                    # logger.info(f"Y支股票 {stock_code}: 精选板块{premium_sectors_for_stock}, 全部板块{sectors}")
                else:
                    # logger.debug(f"Y支股票 {stock_code} 在最近6天（排除当日）无精选板块关系，排除")
                    pass

            # logger.info(f"Y支股票（未涨停但属于精选板块）：{len(y_stock_codes)}只候选，{y_stocks_with_sectors}只有效，{len(Y_stocks)}条记录（基于最近6天，排除当日）")
            print(f"Y支股票（未涨停但属于精选板块）：{len(y_stock_codes)}只候选，{y_stocks_with_sectors}只有效，{len(Y_stocks)}条记录（基于最近6天，排除当日）")
        else:
            # logger.info("无Y支股票候选")
            print("无Y支股票候选")

        # 步骤五：合并X支和Y支股票
        D = X_stocks + Y_stocks
        if not D:
            logger.记录错误("无符合条件的股票（X支+Y支）")
            print("无符合条件的股票（X支+Y支）")
            return
        # logger.info(f"合并后股票总数：{len(D)}（X支:{len(X_stocks)}, Y支:{len(Y_stocks)}）")
        print(f"合并后股票总数：{len(D)}（X支:{len(X_stocks)}, Y支:{len(Y_stocks)}）")

        # 步骤六：生成最终数据（使用板块涨停表的板块评分，不是综合评分）
        # 6.1 获取板块涨停表的最新评分数据
        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        cursor.execute('''
            SELECT "板块名称", "板块评分" FROM "板块涨停表"
            WHERE "日期" = %s
        ''', (today,))
        sector_scores = dict(cursor.fetchall())
        # logger.info(f"获取板块评分数据：{len(sector_scores)}个板块")
        print(f"获取板块评分数据：{len(sector_scores)}个板块")

        # 6.2 计算接力值（基于板块涨停表的板块评分）
        summed_data = {}
        stock_types = {}  # 记录股票类型

        # 记录接力值计算过程
        calculation_log = []

        # 处理X支股票（已涨停）
        for stock_code, sector in X_stocks:
            if sector in sector_scores:
                sector_score = sector_scores[sector]
                key = (today, stock_code)
                summed_data[key] = summed_data.get(key, 0) + sector_score
                stock_types[stock_code] = 'X支'
                calculation_log.append(f"X支 {stock_code} 所属板块 {sector} 板块评分 {sector_score}")
            else:
                # logger.warning(f"板块 {sector} 在板块涨停表中无评分数据")
                pass

        # 处理Y支股票（未涨停）
        for stock_code, sector in Y_stocks:
            if sector in sector_scores:
                sector_score = sector_scores[sector]
                key = (today, stock_code)
                summed_data[key] = summed_data.get(key, 0) + sector_score
                stock_types[stock_code] = 'Y支'
                calculation_log.append(f"Y支 {stock_code} 所属板块 {sector} 板块评分 {sector_score}")
            else:
                # logger.warning(f"板块 {sector} 在板块涨停表中无评分数据")
                pass

        E_clean = [(k[0], k[1], v, stock_types.get(k[1], 'Unknown')) for k, v in summed_data.items()]
        if E_clean:
            # logger.info(f"最终写入数据示例：{E_clean[:3]}")
            print(f"最终写入数据示例：{E_clean[:3]}")
            
            # 记录详细的接力值计算日志
            with open("接力值计算明细.log", "a") as f:
                f.write(f"\n--- {datetime.now()} ---\n")
                f.write(f"日期: {today}\n")
                f.write("接力值计算过程:\n")
                for log in calculation_log:
                    f.write(f"{log}\n")
                f.write("\n最终结果:\n")
                for date, code, value, stock_type in E_clean[:10]:  # 只记录前10条
                    f.write(f"{code}({stock_type}): {value}\n")
                f.write(f"总计: {len(E_clean)}条数据\n")
                f.write(f"接力值范围: {min(c[2] for c in E_clean)}-{max(c[2] for c in E_clean)}\n")

            try:
                # {{ AURA-X: Modify - 修改为PostgreSQL兼容的事务语法. Approval: 寸止(ID:1735002400). }}
                cursor.execute("BEGIN")

                # 检查表结构，如果没有股票类型字段则添加
                # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
                cursor.execute("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name = '个股接力表' AND table_schema = 'public'
                """)
                columns = [row[0] for row in cursor.fetchall()]
                if '股票类型' not in columns:
                    cursor.execute('ALTER TABLE "个股接力表" ADD COLUMN "股票类型" TEXT DEFAULT \'X支\'')
                    # logger.info("添加股票类型字段到个股接力表")

                cursor.execute('DELETE FROM "个股接力表" WHERE "日期" = %s', (today,))
                cursor.executemany('''
                    INSERT INTO "个股接力表"
                    ("日期", "股票代码", "接力", "股票类型")
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT ("日期", "股票代码") DO UPDATE SET
                    "接力" = EXCLUDED."接力",
                    "股票类型" = EXCLUDED."股票类型"
                ''', E_clean)
                conn.commit()

                # 统计X支和Y支股票数量
                x_count = len([e for e in E_clean if e[3] == 'X支'])
                y_count = len([e for e in E_clean if e[3] == 'Y支'])

                logger.记录模块执行(f"接力算法计算完成（X支:{x_count}, Y支:{y_count}），接力值范围：{min(c[2] for c in E_clean):.2f}-{max(c[2] for c in E_clean):.2f}", len(E_clean), "L2-板块涨停表")
                print(f"成功写入 {len(E_clean)} 条数据（X支:{x_count}, Y支:{y_count}），接力值范围：{min(c[2] for c in E_clean)}-{max(c[2] for c in E_clean)}")

                # 🔄 个股接力表更新完成后，自动触发接力DAT文件生成模块
                _trigger_relay_dat_generation()

                return True  # 成功执行
            # {{ AURA-X: Modify - 修改为PostgreSQL异常处理. Approval: 寸止(ID:1737734400). }}
            except psycopg2.Error as e:
                conn.rollback()
                logger.记录错误(f"写入数据失败", e)
                raise
        else:
            logger.记录错误("无符合条件的数据需要写入")
            print("无符合条件的数据需要写入")
            return False

    except psycopg2.Error as e:
        logger.记录错误(f"PostgreSQL数据库错误", e)
        print(f"PostgreSQL数据库错误: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

def _trigger_relay_dat_generation():
    """触发接力DAT文件生成模块"""
    try:
        # 触发接力DAT文件生成模块
        from 数据库写大智慧.接力 import main as 接力DAT_main

        logger.记录模块执行("自动触发接力DAT文件生成模块")

        success, message, count = 接力DAT_main()
        if success:
            logger.记录模块执行("接力DAT文件生成模块执行成功", count, "L5-个股接力表")
        else:
            logger.记录错误(f"接力DAT文件生成模块执行失败: {message}")

    except Exception as e:
        logger.记录错误(f"触发接力DAT文件生成模块异常: {str(e)}")

if __name__ == "__main__":
    main()