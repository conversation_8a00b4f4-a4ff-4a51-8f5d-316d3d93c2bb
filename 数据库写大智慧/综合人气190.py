# ===================== c_dat.py =====================
# {{ AURA-X: Modify - 将SQLite连接改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
import os
import psycopg2
import psycopg2.extras
import struct
import sys
import time
import pytz
from pytz import AmbiguousTimeError
from datetime import datetime
from pathlib import Path

# 获取当前脚本文件所在的目录 (数据库写大智慧)
script_dir = Path(__file__).resolve().parent
# 获取项目根目录 (数据库写大智慧 的父目录)
project_root = script_dir.parent
# 如果项目根目录不在sys.path中，则添加它
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器
logger = 获取日志器("综合人气190")

def generate_timestamp(trading_date):
    tz_sh = pytz.timezone('Asia/Shanghai')
    naive_dt = datetime(trading_date.year, trading_date.month, trading_date.day, 8, 0, 0)
    try:
        dt = tz_sh.localize(naive_dt).astimezone(pytz.utc)
    except AmbiguousTimeError:
        dt = tz_sh.localize(naive_dt, is_dst=False).astimezone(pytz.utc)
    return int(dt.timestamp())

def update_dat_file(filepath, target_ts, new_value):
    try:
        with open(filepath, 'rb+') as f:
            while True:
                pos = f.tell()
                record = f.read(8)
                if not record: break
                if len(record) != 8:
                    return 'corrupt'

                current_ts = struct.unpack('<I', record[:4])[0]
                if current_ts == target_ts:
                    f.seek(pos + 4)
                    f.write(struct.pack('<f', new_value))
                    return 'update'

            f.write(struct.pack('<I', target_ts) + struct.pack('<f', new_value))
            return 'append'

    except FileNotFoundError:
        with open(filepath, 'ab') as f:
            f.write(struct.pack('<I', target_ts) + struct.pack('<f', new_value))
            return 'create'
    except struct.error:
        return 'corrupt'
    except Exception as e:
        raise

def main_original():
    stats = {
        'date_range': None, 'processed': 0, 'update': 0,
        'append': 0, 'create': 0, 'corrupt': 0, 'errors': 0
    }

    try:
        # 导入配置管理
        # {{ AURA-X: Modify - 修改为PostgreSQL连接配置. Approval: 寸止(ID:1737734400). }}
        try:
            from 公共模块.配置管理 import get_config
            config = get_config()
            dat_dir = Path(config.get('data_sources.dzh_base_path')) / '190人气'
        except ImportError:
            # 使用相对路径作为降级方案
            dat_dir = Path(project_root / "data" / "190人气")
            dat_dir.mkdir(parents=True, exist_ok=True)

        # {{ AURA-X: Modify - 统一PostgreSQL密码为标准密码. Approval: 寸止(ID:1737734400). }}
        # {{ AURA-X: Modify - 使用统一配置管理而非硬编码. Approval: 寸止(ID:1737734400). }}
        try:
            from 公共模块.配置管理 import get_config
            config = get_config()
            db_config = config.get('database')
        except ImportError:
            # 备用配置
            db_config = {
                'host': 'localhost',
                'port': 5432,
                'database': 'tradefusion',
                'user': 'postgres',
                'password': 'ymjatTUU520'
            }

        from 公共模块.交易日期 import get_trading_date
        current_trading_date = datetime.strptime(str(get_trading_date()), '%Y%m%d').date()

        pass

        # 修改为只处理当前交易日
        trading_dates = [current_trading_date]  # 原代码包含prev+current，现只保留current
        stats['date_range'] = f"{trading_dates[0].strftime('%Y%m%d')}"

        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        with psycopg2.connect(**db_config) as conn:
            cursor = conn.cursor()
            for date_obj in trading_dates:
                cursor.execute('SELECT "股票代码", "综合人气评分" FROM "个股人气表" WHERE "日期" = %s', (int(date_obj.strftime('%Y%m%d')),))
                records = cursor.fetchall()

                if not records:
                    return False, stats  # 无数据时返回失败

                for stock_code, score in records:
                    stats['processed'] += 1
                    try:
                        filepath = dat_dir / f"{stock_code.strip()}.dat"
                        target_ts = generate_timestamp(date_obj)
                        result = update_dat_file(filepath, target_ts, float(score))

                        if result in stats:
                            stats[result] += 1
                        else:
                            stats['errors'] += 1
                    except Exception as e:
                        stats['errors'] += 1

        return True, stats  # 成功时返回统计信息

    except Exception as e:
        return False, stats  # 异常时返回失败

def main():
    try:
        success, stats = main_original()
        trading_date = stats['date_range']
        total = stats['processed']
        if success:
            logger.记录模块执行("DAT文件生成完成", stats['processed'])
        else:
            logger.记录错误(f"DAT文件生成失败 - 错误{stats['errors']}个")
        return "success" if success else "failed"
    except Exception as e:
        error_msg = f"DAT处理异常: {str(e)}"
        pass
        return f"failed: {error_msg}"

if __name__ == '__main__':
    result = main()
    pass