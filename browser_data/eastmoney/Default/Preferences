{"accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "cmn-Hans-CN"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 139}, "autofill": {"last_version_deduped": 139}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"window_placement": {"bottom": 1090, "left": 10, "maximized": false, "right": 1930, "top": 10, "work_area_bottom": 600, "work_area_left": 0, "work_area_right": 800, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "data_sharing": {"eligible_for_version_out_of_date_instant_message": false, "eligible_for_version_out_of_date_persistent_message": false, "has_shown_any_version_out_of_date_message": false}, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "35eb81ea-3901-4e90-989b-72ed5c66cf59", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "139.0.7258.5"}, "gaia_cookie": {"changed_time": **********.256838, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_binary_data": "", "periodic_report_time_2": "*****************"}, "gcm": {"product_category_for_subtypes": "org.chromium.windows"}, "google": {"services": {"signin_scoped_device_id": "561fe0b8-7a3e-4f32-8b03-cad5ed772b80"}}, "intl": {"selected_languages": "zh-CN,zh"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"engagement": {"schema_version": 5}}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 3}, "optimization_guide": {"previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_TRACKING": true, "SAVED_TAB_GROUP": true, "V8_COMPILE_HINTS": true}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]baidu.com,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]eastmoney.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {"https://vipmoney.eastmoney.com:443,*": {"last_modified": "*****************", "setting": {"added_time": "*****************", "enabled": true}}}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://vipmoney.eastmoney.com:443,*": {"expiration": "13406874048813889", "last_modified": "13399098048813897", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 28}}, "https://www.baidu.com:443,*": {"expiration": "13406188315451276", "last_modified": "13398412315451281", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://vipmoney.eastmoney.com:443,*": {"last_modified": "13399098041531426", "setting": {"lastEngagementTime": 1.3399098041531398e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 9.253951636846933, "rawScore": 100.0}}, "https://www.baidu.com:443,*": {"last_modified": "13399088737629722", "setting": {"lastEngagementTime": 1.3398736122909608e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "139.0.7258.5", "creation_time": "13398412291261622", "exit_type": "Normal", "family_member_role": "not_in_family", "isolated_web_app": {"install": {"pending_initialization_count": 0}}, "last_engagement_time": "13399098041531398", "last_time_obsolete_http_credentials_removed": 1753944042.011687, "last_time_password_store_metrics_reported": 1754615167.01241, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "您的 Chromium", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13399090167", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "CmQKC3NlYXJjaF91c2VyElUKSg0AAAAAEO+SkNHhyuYXGjgKMBouCgoNAACAPxIDTG93Cg0NAACgQBIGTWVkaXVtCgsNAACwQRIESGlnaBIETm9uZRIEEAcYBCACEJuTkNHhyuYXCmAKEXJlc3VtZV9oZWF2eV91c2VyEksKQA0AAAAAELePkNHhyuYXGi4KJgokDQAAAD8SFlJlc3VtZUhlYXZ5VXNlclNlZ21lbnQaBU90aGVyEgQQDhgEIAIQ1I+Q0eHK5hcK5QIKEWNyb3NzX2RldmljZV91c2VyEs8CCsMCDQAAgD8QpOqltOS95hcasAIKpwIapAIKGQ0AAIA/EhJOb0Nyb3NzRGV2aWNlVXNhZ2UKGA0AAABAEhFDcm9zc0RldmljZU1vYmlsZQoZDQAAQEASEkNyb3NzRGV2aWNlRGVza3RvcAoYDQAAgEASEUNyb3NzRGV2aWNlVGFibGV0CiINAACgQBIbQ3Jvc3NEZXZpY2VNb2JpbGVBbmREZXNrdG9wCiENAADAQBIaQ3Jvc3NEZXZpY2VNb2JpbGVBbmRUYWJsZXQKIg0AAOBAEhtDcm9zc0RldmljZURlc2t0b3BBbmRUYWJsZXQKIA0AAABBEhlDcm9zc0RldmljZUFsbERldmljZVR5cGVzChcNAAAQQRIQQ3Jvc3NEZXZpY2VPdGhlchISTm9Dcm9zc0RldmljZVVzYWdlEgQQBxgEIAIQ3eqltOS95hcKUgoNc2hvcHBpbmdfdXNlchJBCjYNAAAAABDA74GApcrmFxokChwKGg0AAAA/EgxTaG9wcGluZ1VzZXIaBU90aGVyEgQQAhgEIAMQnp7ojavK5hcKcwoVcGFzc3dvcmRfbWFuYWdlcl91c2VyEloKTw0AAAAAEPuRkNHhyuYXGj0KNQozDQAAAD8SE1Bhc3N3b3JkTWFuYWdlclVzZXIaF05vdF9QYXNzd29yZE1hbmFnZXJVc2VyEgQQBxgEIAEQsZKQ0eHK5hc=", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13398998399000000", "uma_in_sql_start_time": "13398412291289560"}, "sessions": {"event_log": [{"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13398847109237386", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398847305958198", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13398852294259626", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398917367544682", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13398924949173531", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398930960375260", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13398937468228041", "type": 2, "window_count": 1}, {"crashed": false, "time": "13399002219099792", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13399003831743961", "type": 2, "window_count": 1}, {"crashed": false, "time": "13399003860000246", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13399018484763026", "type": 2, "window_count": 1}, {"crashed": false, "time": "13399018496638838", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13399022339542027", "type": 2, "window_count": 1}, {"crashed": false, "time": "13399022549503227", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13399022620648798", "type": 2, "window_count": 1}, {"crashed": false, "time": "13399022638695646", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13399024222329359", "type": 2, "window_count": 1}, {"crashed": false, "time": "13399088737010716", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13399098048796102", "type": 2, "window_count": 1}], "session_data_status": 5}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "site_search_settings": {"overridden_keywords": []}, "spellcheck": {"dictionaries": ["zh-CN", "en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "139"}}