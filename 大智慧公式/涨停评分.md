

{1. 涨停时间	09:35前+3分 / 11:30前+2分 / 14:00前+1分 / 尾盘+0.5分   退潮期尾盘涨停权重降至0.3倍   权重20%}
真实涨停时间:="kingwa@ReadData"(c,11);
涨停时间评分:= IF(BETWEEN(真实涨停时间,91500,93500)  , 3  ,
              IF(BETWEEN(真实涨停时间,93500,113000)  , 2  ,
              IF(BETWEEN(真实涨停时间,113000,140000)  , 1  ,
              IF(BETWEEN(真实涨停时间,140000,150000)  , 0.5  ,
              0  ))));   

{2. 封单质量	(封单金额/流通市值) >5%→+3分；3-5%→+2分；<1%→0分；撤单率>50%→-2分   权重20%}
封单金额:=DYNAINFO(25);
自由流通市值:=CAPITALFREE;
真实封流比:=封单金额/自由流通市值;

封单质量评分:= IF(真实封流比>=5,  3  ,
              IF(BETWEEN(真实封流比,3,5)  , 2  ,
              IF(BETWEEN(真实封流比,3,1)  , 1  ,
              0))) ;

{4. 换手健康度	换手率20%-40%→+2分；<10%→0分；>60%→-1分	权重15%}
真实换手:=V/自由流通市值;
真实换手评分:= 
    IF(BETWEEN(真实换手,10,20), 1,
        IF(BETWEEN(真实换手,20,40), 3,
            IF(BETWEEN(真实换手,40,60), 2,
                IF(真实换手>=60, -2, -1)
            )
        )
    ) ;



{
3. 连板高度	当前连板天数×2分（3连板=6分）	25%↑	突出市场地位核心指标
5. 累计连板	历史断板后重新累计（3+断+2→累计5分）		10%↑	增强对穿越龙的识别
}

连板数:=LBTIMES;
连板数真实:=SUM( ZTTIMES(1,0) , 连板数);
断板:=LBTIMES>连板数真实;
断板评分:=IF(断板,连板数,0) ;
{阶梯奖励计算}
分段1奖励:=IF(连板数>4, (MIN(连板数,5)-4)*2,0); {5板区间}
分段2奖励:=IF(连板数>5, (MIN(连板数,7)-5)*2,0); {6-7板区间}
分段3奖励:=IF(连板数>7, (连板数-7)*3,0); {8板+区间}
总阶梯奖励:=(分段1奖励+分段2奖励+分段3奖励);
基础分:=连板数*2;
正常连板评分:=(基础分+总阶梯奖励) ;

{6. 炸板负反馈	当日炸板×(-2)；3日内炸板股再涨停→权重×0.8	10%	机构买入抵消50%扣分}
炸板3:=COUNT(H=ZTPRICE AND C<ZTPRICE ,3)>0 ;
炸板扣分:=IF(炸板3>0,0.8,1);



涨停评分:=( 涨停时间评分 *0.2  +  封单质量评分 *0.2  +  真实换手评分*0.15  +    IF(断板,断板评分*0.1,正常连板评分*0.25)  )*炸板扣分;

Z12:="kingwa@savedata"(涨停评分, 12 );
Z15:="kingwa@savedata"(真实换手评分=3, 15 );



