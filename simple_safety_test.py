#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的安全机制测试
"""
import sys
from pathlib import Path

# 设置项目路径
project_root = Path(__file__).absolute().parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from 数据2_网络采集.个股人气表 import PopularityDatabase
from 公共模块.交易日期 import get_trading_date
import psycopg2

def simple_test():
    """简单测试安全机制"""
    
    try:
        date = get_trading_date()
        print(f"=== 安全机制简单测试 (日期: {date}) ===\n")
        
        # 创建数据库实例
        db = PopularityDatabase()
        
        # 测试数据源完整性检查
        print("🔍 测试数据源完整性检查...")
        should_cleanup = db._should_trigger_cleanup(date)
        print(f"检查结果: {'✅ 可以安全删除' if should_cleanup else '❌ 不能删除，数据源不完整'}")
        
        # 查看当前数据状态
        conn = psycopg2.connect(
            host='localhost', port=5432, database='tradefusion', 
            user='postgres', password='ymjatTUU520'
        )
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM "个股人气表" WHERE "日期" = %s AND "东财人气排名" IS NOT NULL', (date,))
        dcf_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM "个股人气表" WHERE "日期" = %s AND "同花人气排名" IS NOT NULL', (date,))
        ths_count = cursor.fetchone()[0]
        
        print(f"\n📊 当前数据状态:")
        print(f"   东财数据: {dcf_count} 条")
        print(f"   同花数据: {ths_count} 条")
        
        # 分析安全机制的逻辑
        if dcf_count > 0 and ths_count > 0:
            print(f"   ✅ 两个数据源都存在，安全机制允许删除")
        elif dcf_count > 0 and ths_count == 0:
            print(f"   ⚠️ 只有东财数据，安全机制应该阻止删除")
        elif dcf_count == 0 and ths_count > 0:
            print(f"   ⚠️ 只有同花数据，安全机制应该阻止删除")
        else:
            print(f"   ❌ 没有任何数据源")
        
        # 验证安全机制是否正确工作
        expected_result = dcf_count > 0 and ths_count > 0
        if should_cleanup == expected_result:
            print(f"   ✅ 安全机制工作正常")
        else:
            print(f"   ❌ 安全机制异常")
        
        # 关闭数据库连接
        if hasattr(db, 'conn') and db.conn:
            db.conn.close()
        conn.close()
        
        print(f"\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_test()
