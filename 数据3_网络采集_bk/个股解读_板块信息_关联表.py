# {{ AURA-X: Modify - 将SQLite连接改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
import psycopg2
import psycopg2.extras
from psycopg2.extensions import connection as Connection, cursor as Cursor
import logging
import os
import sys
from pathlib import Path
from typing import List, Set, Dict, Tuple, Optional, Any
from datetime import datetime
from logging.handlers import TimedRotatingFileHandler

# 添加项目根目录到路径
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器

# 获取统一日志器
logger = 获取日志器("个股解读_板块信息_关联表")

class DataImporter:
    def __init__(self, db_config: Optional[Dict[str, Any]] = None):
        # {{ AURA-X: Modify - 修改为PostgreSQL连接配置. Approval: 寸止(ID:1737734400). }}
        # {{ AURA-X: Modify - 使用统一配置管理而非硬编码. Approval: 寸止(ID:1737734400). }}
        if db_config is None:
            try:
                from 公共模块.配置管理 import get_config
                config = get_config()
                self.db_config = config.get('database')
            except ImportError:
                # 备用配置
                self.db_config = {
                    'host': 'localhost',
                    'port': 5432,
                    'database': 'tradefusion',
                    'user': 'postgres',
                    'password': 'ymjatTUU520'
                }
        else:
            self.db_config = db_config
        self.conn: Optional[Connection] = None
        self.stock_cache: Dict[str, str] = {}  # key: date-code, value: 个股解读
        self.sector_cache: Dict[str, Tuple[float, str]] = {}  # key: date-sector, value: (涨幅, 消息)

    def _get_cursor(self) -> Cursor:
        """获取数据库游标，确保连接有效"""
        if self.conn is None:
            raise Exception("数据库连接未建立")
        return self.conn.cursor()

    def connect(self):
        """建立数据库连接"""
        # {{ AURA-X: Modify - 修改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
        try:
            if not self.conn:
                self.conn = psycopg2.connect(**self.db_config)
                if self.conn:
                    self.conn.autocommit = False
        except Exception as e:
            logger.error(f"✗ <个股解读_板块信息_关联表> 数据库连接失败: {str(e)}")
            self.conn = None
            raise RuntimeError(f"数据库连接失败: {str(e)}")

    def check_connection(self):
        """验证数据库连接状态"""
        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        if not self.conn:
            self.connect()
        try:
            cursor = self._get_cursor()
            cursor.execute('SELECT 1')
            cursor.close()
            return True
        except (psycopg2.Error, psycopg2.ProgrammingError):
            self.connect()
            return self.conn is not None

    def initialize_cache(self):
        """初始化缓存"""
        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        if not self.check_connection():
            raise RuntimeError("数据库连接未建立")

        try:
            cursor = self._get_cursor()

            # 获取个股解读数据
            cursor.execute('SELECT "日期", "股票代码", "个股解读" FROM "个股解读表"')
            for row in cursor.fetchall():
                self.stock_cache[f"{row[0]}-{row[1]}"] = row[2]

            # 获取板块信息数据
            cursor.execute('SELECT "日期", "板块名称", "板块涨幅", "板块消息" FROM "板块信息表"')
            for row in cursor.fetchall():
                self.sector_cache[f"{row[0]}-{row[1]}"] = (row[2], row[3])

            cursor.close()
        except Exception as e:
            logger.error(f"✗ <个股解读_板块信息_关联表> 缓存初始化失败: {str(e)}")
            raise

    def import_from_temp_table(self):
        """从临时表导入数据到数据库（主要方案）"""
        try:
            from 数据2_网络采集.人气临时表管理 import 获取临时表管理器
            temp_manager = 获取临时表管理器()

            # 从临时表读取清洗后的数据
            rows = temp_manager.读取选股宝清洗待处理数据()
            if not rows:
                logger.error(f"✗ <个股解读_板块信息_关联表> 临时表中无待处理数据")
                temp_manager.close()
                return False

            # 调用者先输出（精简：移除启动日志）
            # logger.info(f"[个股解读_板块信息_关联表] 开始数据库导入处理 (由[选股宝清洗]调用)")

            # 读取并分组数据
            stock_data: Dict[str, Dict[str, str]] = {}  # key: date-code, value: stock info
            sector_data: Dict[str, Dict[str, Any]] = {}  # key: date-sector, value: sector info
            relation_data: Dict[str, Set[str]] = {}  # key: date-code, value: set of sectors

            # 获取数据中的日期
            date_list = [row[0] for row in rows]  # 第一列是日期
            dates = set(date_list)

            # 处理每一行数据
            for i, row in enumerate(rows):
                try:
                    date, stock_code, sector, sector_change_str, sector_message, stock_description = row

                    stock_code = stock_code.strip()
                    sector_with_change = sector.strip()

                    # 提取纯板块名称（移除涨跌幅信息）
                    import re
                    sector = re.sub(r'\s*[+\-]\d+\.\d+%$', '', sector_with_change).strip()

                    # 存储个股信息
                    stock_key = f"{date}-{stock_code}"
                    if stock_key not in stock_data:
                        stock_data[stock_key] = {
                            'date': date,
                            'code': stock_code,
                            'description': stock_description
                        }

                    # 存储板块信息
                    sector_key = f"{date}-{sector}"
                    if sector_key not in sector_data:
                        sector_change = sector_change_str.replace('+', '').replace('%', '').strip()
                        try:
                            sector_change_value = float(sector_change)
                        except ValueError:
                            sector_change_value = 0.0

                        sector_data[sector_key] = {
                            'date': date,
                            'name': sector,
                            'change': sector_change_value,
                            'message': sector_message
                        }

                    # 存储关联关系
                    if stock_key not in relation_data:
                        relation_data[stock_key] = set()
                    relation_data[stock_key].add(sector)

                except Exception as e:
                    logger.error(f"✗ <个股解读_板块信息_关联表> 处理第{i+1}行数据失败: {str(e)}")
                    raise

            # 执行数据库操作
            # {{ AURA-X: Modify - 修改为PostgreSQL语法和事务处理. Approval: 寸止(ID:1737734400). }}
            cursor = self._get_cursor()
            try:
                # 删除旧数据
                for date in dates:
                    # 删除个股解读数据
                    delete_stock_sql = '''
                        DELETE FROM "个股解读表"
                        WHERE "日期" = %s
                    '''
                    cursor.execute(delete_stock_sql, [date])

                    # 删除板块信息数据
                    delete_sector_sql = '''
                        DELETE FROM "板块信息表"
                        WHERE "日期" = %s
                    '''
                    cursor.execute(delete_sector_sql, [date])

                    # 删除关联表数据
                    for stock_key, sectors in relation_data.items():
                        if str(date) in stock_key:
                            stock_code = stock_key.split('-')[1]
                            delete_relation_sql = '''
                                DELETE FROM "个股板块关联表"
                                WHERE "日期" = %s
                                AND "股票代码" = %s
                            '''
                            cursor.execute(delete_relation_sql, [date, stock_code])

                # 插入新数据
                # 1. 插入个股解读
                stock_insert_sql = '''
                    INSERT INTO "个股解读表" ("日期", "股票代码", "个股解读")
                    VALUES (%s, %s, %s)
                '''
                stock_params = [(info['date'], info['code'], info['description'])
                              for info in stock_data.values()]
                cursor.executemany(stock_insert_sql, stock_params)

                # 2. 插入板块信息
                sector_insert_sql = '''
                    INSERT INTO "板块信息表" ("日期", "板块名称", "板块涨幅", "板块消息")
                    VALUES (%s, %s, %s, %s)
                '''
                sector_params = [(info['date'], info['name'], info['change'], info['message'])
                               for info in sector_data.values()]
                cursor.executemany(sector_insert_sql, sector_params)

                # 3. 插入关联关系
                relation_insert_sql = '''
                    INSERT INTO "个股板块关联表" ("日期", "股票代码", "所属板块名称")
                    VALUES (%s, %s, %s)
                '''
                relation_params = []
                for stock_key, sectors in relation_data.items():
                    date, code = stock_key.split('-')
                    for sector in sectors:
                        relation_params.append((int(date), code, sector))
                cursor.executemany(relation_insert_sql, relation_params)

                # 提交事务
                if self.conn:
                    self.conn.commit()

            except Exception as db_error:
                # 回滚事务
                if self.conn:
                    self.conn.rollback()
                if 'cursor' in locals():
                    cursor.close()
                raise db_error
            finally:
                if 'cursor' in locals():
                    cursor.close()

            # 标记临时表数据为已处理
            temp_manager.标记选股宝清洗数据已处理()
            temp_manager.close()

            # 被调用者后输出（完整颜色版）
            logger.info(f"\033[91m[个股解读_板块信息_关联表]\033[0m 数据库导入完成 - \033[93m个股解读表\033[0m+\033[92m{len(stock_data):,}条\033[0m|\033[93m板块信息表\033[0m+\033[92m{len(sector_data):,}条\033[0m|\033[93m个股板块关联表\033[0m+\033[92m{len(relation_params):,}条\033[0m (由\033[91m[选股宝清洗]\033[0m调用)")

            # 🔄 数据导入完成后，自动触发选股宝STR文件生成模块
            _trigger_str_generation()

            return True

        except Exception as e:
            logger.error(f"✗ <个股解读_板块信息_关联表> 从临时表导入失败: {str(e)}")
            return False



    def __del__(self):
        if self.conn:
            self.conn.close()

def _trigger_str_generation():
    """触发选股宝STR文件生成模块"""
    try:
        # 触发选股宝_大智慧str模块
        from 数据库写大智慧.选股宝_大智慧str import generate_str_files

        logger.info(f"🔄 [个股解读_板块信息_关联表] 自动触发选股宝STR文件生成模块...")

        # 调用STR文件生成函数
        generate_str_files()
        logger.info(f"✅ [个股解读_板块信息_关联表] 选股宝STR文件生成模块执行成功")

    except Exception as e:
        logger.error(f"❌ [个股解读_板块信息_关联表] 触发STR文件生成模块异常: {str(e)}")

def main_from_temp_table():
    """主函数入口，从临时表导入数据（主要方案）"""
    importer = DataImporter()
    try:
        importer.connect()
        importer.initialize_cache()
        result = importer.import_from_temp_table()
        return result
    except Exception as e:
        logger.error(f"✗ <个股解读_板块信息_关联表> 临时表主函数执行失败: {str(e)}")
        return False
    finally:
        if hasattr(importer, 'conn') and importer.conn:
            importer.conn.close()

def main(csv_path: Optional[str] = None):
    """主函数入口，优先使用临时表，备用CSV文件"""

    # 优先尝试临时表方案
    try:
        result = main_from_temp_table()
        if result:
            return True
    except Exception as e:
        logger.error(f"✗ <个股解读_板块信息_关联表> 临时表方案失败: {str(e)}")
        return False

if __name__ == "__main__":
    main()