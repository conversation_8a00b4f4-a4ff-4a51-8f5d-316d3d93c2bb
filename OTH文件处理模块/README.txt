OTH文件处理模块
================

本模块包含大智慧OTH文件格式的完整处理工具集。

文件说明：
---------

【生产级工具】
1. OTH读取工具.py
   - 功能：生产级OTH文件读取器
   - 用途：读取OTH文件并转换为DataFrame格式
   - 特点：错误处理完善，支持批量处理，CSV导出
   - 适用：数据导入、分析、转换

2. OTH写入工具.py
   - 功能：生产级OTH文件生成器
   - 用途：创建符合大智慧标准的OTH文件
   - 特点：多种数据源支持，格式验证，自定义配置
   - 适用：数据导出、文件生成、格式转换

OTH文件格式规范：
---------------
- 记录结构：8字节/记录
- 时间戳：前4字节，Unix时间戳（小端序）
- 数值：后4字节，32位浮点数（小端序）
- 文件大小：必须是8的倍数

使用示例：
---------
# 读取OTH文件
from OTH读取工具 import OTHReader
reader = OTHReader()
df = reader.read_oth_file("文件路径.oth")

# 写入OTH文件
from OTH写入工具 import OTHWriter
writer = OTHWriter()
writer.write_oth_file("输出路径.oth", dataframe_or_list)

# 命令行使用
python OTH读取工具.py    # 读取示例文件
python OTH写入工具.py    # 生成示例文件

开发日期：2025-08-07
开发者：TradeFusion项目组
