#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OTH读取工具 - 生产级大智慧OTH文件读取器
专门用于读取和解析OTH文件的生产级工具
"""

import struct
import os
import pandas as pd
from datetime import datetime
from typing import Optional, List, Dict

class OTHReader:
    """生产级OTH文件读取器"""
    
    def __init__(self):
        self.last_error = None
        
    def read_oth_file(self, file_path: str) -> Optional[pd.DataFrame]:
        """
        读取OTH文件并返回DataFrame
        
        Args:
            file_path: OTH文件路径
            
        Returns:
            包含datetime索引和value列的DataFrame，失败返回None
        """
        try:
            if not os.path.exists(file_path):
                self.last_error = f"文件不存在: {file_path}"
                return None
                
            with open(file_path, 'rb') as f:
                data = f.read()
                
            # 验证文件格式
            if len(data) % 8 != 0:
                self.last_error = f"文件格式错误，大小不是8的倍数: {len(data)}"
                return None
                
            record_count = len(data) // 8
            timestamps = []
            values = []
            
            # 解析每条记录
            for i in range(record_count):
                offset = i * 8
                time_raw, value_raw = struct.unpack('<2I', data[offset:offset+8])
                
                # 验证时间戳范围
                try:
                    if 946684800 <= time_raw <= 2147483647:  # 2000-2038年
                        dt = datetime.fromtimestamp(time_raw)
                        timestamps.append(dt)
                        
                        # 解析浮点数值
                        value = struct.unpack('<f', struct.pack('<I', value_raw))[0]
                        values.append(value)
                except (ValueError, OSError):
                    continue
            
            if not timestamps:
                self.last_error = "文件中没有有效的数据记录"
                return None
            
            # 创建DataFrame
            df = pd.DataFrame({
                'datetime': timestamps,
                'value': values
            })
            
            df.set_index('datetime', inplace=True)
            df.sort_index(inplace=True)
            
            print(f"✅ 读取成功: {os.path.basename(file_path)}")
            print(f"📊 记录数: {len(df)}")
            print(f"📅 时间范围: {df.index.min()} - {df.index.max()}")
            print(f"📈 数值范围: {df['value'].min():.1f} - {df['value'].max():.1f}")
            
            return df
            
        except Exception as e:
            self.last_error = f"读取失败: {str(e)}"
            return None
    
    def read_multiple_files(self, file_paths: List[str]) -> Dict[str, pd.DataFrame]:
        """
        批量读取多个OTH文件
        
        Args:
            file_paths: OTH文件路径列表
            
        Returns:
            文件名到DataFrame的字典
        """
        results = {}
        
        for file_path in file_paths:
            file_name = os.path.splitext(os.path.basename(file_path))[0]
            df = self.read_oth_file(file_path)
            if df is not None:
                results[file_name] = df
            else:
                print(f"❌ 读取失败: {file_name} - {self.last_error}")
                
        return results
    
    def export_to_csv(self, df: pd.DataFrame, output_path: str) -> bool:
        """
        导出DataFrame到CSV文件
        
        Args:
            df: 要导出的DataFrame
            output_path: 输出CSV文件路径
            
        Returns:
            成功返回True，失败返回False
        """
        try:
            df.to_csv(output_path, encoding='utf-8-sig')
            print(f"✅ 导出成功: {output_path}")
            return True
        except Exception as e:
            self.last_error = f"导出失败: {str(e)}"
            print(f"❌ {self.last_error}")
            return False
    
    def get_data_summary(self, df: pd.DataFrame) -> Dict:
        """
        获取数据摘要统计
        
        Args:
            df: 要分析的DataFrame
            
        Returns:
            包含统计信息的字典
        """
        return {
            'record_count': len(df),
            'start_date': df.index.min(),
            'end_date': df.index.max(),
            'min_value': df['value'].min(),
            'max_value': df['value'].max(),
            'mean_value': df['value'].mean(),
            'std_value': df['value'].std()
        }
    
    def filter_by_date_range(self, df: pd.DataFrame, 
                           start_date: str, end_date: str) -> pd.DataFrame:
        """
        按日期范围过滤数据
        
        Args:
            df: 原始DataFrame
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            过滤后的DataFrame
        """
        try:
            return df.loc[start_date:end_date]
        except Exception as e:
            self.last_error = f"日期过滤失败: {str(e)}"
            print(f"❌ {self.last_error}")
            return df
    
    def get_latest_records(self, df: pd.DataFrame, count: int = 10) -> pd.DataFrame:
        """
        获取最新的N条记录
        
        Args:
            df: DataFrame
            count: 记录数量
            
        Returns:
            最新的N条记录
        """
        return df.tail(count)

def main():
    """主函数 - 演示用法"""
    print("📖 OTH读取工具 - 生产级")
    print("=" * 40)
    
    reader = OTHReader()
    
    # 测试文件列表
    test_files = [
        r"E:\dzh2\USERDATA\SelfData\550A股涨停.oth",
        r"E:\dzh2\USERDATA\SelfData\551A股连板数.OTH",
        r"E:\dzh2\USERDATA\SelfData\DATA1002.OTH"
    ]
    
    # 批量读取
    data_dict = reader.read_multiple_files(test_files)
    
    # 处理每个文件的数据
    for file_name, df in data_dict.items():
        print(f"\n📊 {file_name} 数据摘要:")
        print("-" * 40)
        
        summary = reader.get_data_summary(df)
        print(f"记录数: {summary['record_count']}")
        print(f"时间范围: {summary['start_date']} - {summary['end_date']}")
        print(f"数值范围: {summary['min_value']:.1f} - {summary['max_value']:.1f}")
        print(f"平均值: {summary['mean_value']:.1f}")
        
        # 显示最新5天的数据
        print(f"\n最新5天数据:")
        recent_data = reader.get_latest_records(df, 5)
        for idx, row in recent_data.iterrows():
            print(f"  {idx.strftime('%Y-%m-%d')}: {row['value']:.1f}")
        
        # 导出CSV文件
        csv_path = f"{file_name}.csv"
        reader.export_to_csv(df, csv_path)

if __name__ == "__main__":
    main()
