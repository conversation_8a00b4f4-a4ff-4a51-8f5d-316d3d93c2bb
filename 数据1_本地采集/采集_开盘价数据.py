#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
TradeFusion 开盘价数据采集模块
数据源：E:/dzh2/USERDATA/SelfData/23开盘价
目标表：基础数据A
"""

import os
import struct
import sys
import pytz
import time
import signal
import hashlib
import json
from pathlib import Path
from datetime import datetime

# 重新设置项目根目录
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent  # 回退到TradeFusion目录

# 确保项目根目录在系统路径中
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    from 公共模块.交易日期 import get_trading_date
except ImportError as e:
    sys.exit(1)

# 内置数据目录配置
DATA_DIRS = {
    '开盘价': 'E:/dzh2/USERDATA/SelfData/23开盘价',
    '收盘价': 'E:/dzh2/USERDATA/SelfData/24收盘价',
    '最高价': 'E:/dzh2/USERDATA/SelfData/25最高价',
    '最低价': 'E:/dzh2/USERDATA/SelfData/26最低价',
    '涨停价': 'E:/dzh2/USERDATA/SelfData/27涨停价',
    '跌停价': 'E:/dzh2/USERDATA/SelfData/28跌停价',
    '真实换手': 'E:/dzh2/USERDATA/SelfData/29真实换手'
}

# 内置数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'tradefusion',
    'user': 'postgres',
    'password': 'ymjatTUU520'
}

# 内置处理配置
FORBIDDEN_TIME_START = (8, 40)  # 8:40
FORBIDDEN_TIME_END = (9, 20)    # 9:20

# 全局时区对象，避免重复创建
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

# TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器

# 获取统一日志器
logger = 获取日志器("L1-开盘价采集")

# 循环控制变量
running = True
cycle_count = 0
success_count = 0
fail_count = 0
total_data_processed = 0

# 文件变化检测缓存
_file_change_cache = {
    'file_hashes': {},      # 文件路径 -> 哈希值
    'last_check_time': {},  # 文件路径 -> 最后检查时间
    'last_modified': {},    # 文件路径 -> 最后修改时间
    'cache_file': None      # 缓存文件路径
}

def signal_handler(signum, frame):
    """信号处理器：优雅退出"""
    global running
    logger.记录模块执行("接收到退出信号，正在优雅退出")
    logger.记录模块执行(f"最终汇总 - 运行{cycle_count}个周期，{success_count}次成功，{fail_count}次失败", total_data_processed)
    running = False

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def is_in_forbidden_time():
    """检查当前时间是否在禁止运行时段(8:40~9:20)"""
    now = datetime.now(BEIJING_TZ)
    current_time = (now.hour, now.minute)
    return FORBIDDEN_TIME_START <= current_time < FORBIDDEN_TIME_END

def get_current_beijing_time():
    """获取当前北京时间，供其他函数复用"""
    return datetime.now(BEIJING_TZ)

# 交易起始时间缓存
_trading_start_cache = {
    'timestamp': None,
    'trading_date': None,
    'calculated_at': None
}

def get_trading_start_time():
    """
    获取基于交易日期的起始时间点（带缓存优化）
    返回: start_timestamp - 基准日期的上午9:20的时间戳
    """
    global _trading_start_cache

    try:
        # 获取当前交易日期
        current_trading_date = get_trading_date()

        # 检查缓存是否有效
        if (_trading_start_cache['timestamp'] is not None and
            _trading_start_cache['trading_date'] == current_trading_date):
            logger.记录模块执行(f"使用缓存的交易起始时间 (交易日: {current_trading_date})")
            return _trading_start_cache['timestamp']

        # 缓存失效或首次计算，重新计算
        logger.记录模块执行(f"重新计算交易起始时间 (交易日: {current_trading_date})")

        date_str = str(current_trading_date)
        naive_date = datetime.strptime(date_str, "%Y%m%d")
        start_time = BEIJING_TZ.localize(naive_date.replace(hour=9, minute=20, second=0))
        start_timestamp = start_time.timestamp()

        # 更新缓存
        _trading_start_cache = {
            'timestamp': start_timestamp,
            'trading_date': current_trading_date,
            'calculated_at': time.time()
        }

        return start_timestamp

    except Exception as e:
        logger.记录错误(f"获取交易起始时间失败: {e}")
        # 降级处理：使用当前时间前20小时作为备用方案
        current_timestamp = time.time()
        fallback_start = current_timestamp - (20 * 3600)  # 20小时前
        _trading_start_cache = {'timestamp': None, 'trading_date': None, 'calculated_at': None}
        return fallback_start

def init_file_change_cache():
    """初始化文件变化检测缓存"""
    global _file_change_cache

    # 设置缓存文件路径
    cache_dir = Path(__file__).parent / '.cache'
    cache_dir.mkdir(exist_ok=True)
    _file_change_cache['cache_file'] = cache_dir / 'opening_price_cache.json'

    # 尝试加载已有缓存
    try:
        if _file_change_cache['cache_file'].exists():
            with open(_file_change_cache['cache_file'], 'r', encoding='utf-8') as f:
                cached_data = json.load(f)
                _file_change_cache['file_hashes'] = cached_data.get('file_hashes', {})
                _file_change_cache['last_check_time'] = cached_data.get('last_check_time', {})
                _file_change_cache['last_modified'] = cached_data.get('last_modified', {})
            logger.记录模块执行(f"文件变化缓存加载成功，包含{len(_file_change_cache['file_hashes'])}个文件记录")
    except Exception as e:
        logger.记录错误(f"加载文件变化缓存失败: {e}")
        _file_change_cache['file_hashes'] = {}
        _file_change_cache['last_check_time'] = {}
        _file_change_cache['last_modified'] = {}

def save_file_change_cache():
    """保存文件变化检测缓存"""
    global _file_change_cache

    try:
        cache_data = {
            'file_hashes': _file_change_cache['file_hashes'],
            'last_check_time': _file_change_cache['last_check_time'],
            'last_modified': _file_change_cache['last_modified']
        }
        with open(_file_change_cache['cache_file'], 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.记录错误(f"保存文件变化缓存失败: {e}")

def get_file_hash(file_path):
    """
    计算DAT文件中当前交易日数据的哈希值
    专门针对DAT文件格式：只检测target_timestamp对应的数值变化
    """
    try:
        # 获取当前交易日的target_timestamp
        trading_date = get_trading_date()
        date_str = str(trading_date)
        naive_date = datetime.strptime(date_str, "%Y%m%d")
        target_date = BEIJING_TZ.localize(naive_date.replace(hour=8, minute=0, second=0))
        target_timestamp = int(target_date.astimezone(pytz.utc).timestamp())

        # 在DAT文件中查找target_timestamp对应的数值
        target_value = None
        with open(file_path, "rb") as f:
            while True:
                record = f.read(8)
                if not record or len(record) != 8:
                    break

                # 解析8字节记录：4字节时间戳 + 4字节浮点数
                timestamp = int.from_bytes(record[:4], 'little')
                if timestamp == target_timestamp:
                    target_value = struct.unpack('<f', record[4:])[0]
                    break

        # 如果找到目标数据，计算其哈希值
        if target_value is not None:
            hash_data = f"{target_timestamp}:{target_value}".encode('utf-8')
            return hashlib.md5(hash_data).hexdigest()
        else:
            return f"no_data_{target_timestamp}"

    except Exception as e:
        logger.记录错误(f"计算DAT文件哈希失败 {file_path}: {e}")
        return None

def has_file_changed(file_path, start_timestamp):
    """
    检查文件是否发生变化（结合修改时间和内容哈希）
    """
    global _file_change_cache

    try:
        if not os.path.exists(file_path):
            return False

        file_mtime = os.path.getmtime(file_path)

        if file_mtime < start_timestamp:
            return False

        cached_mtime = _file_change_cache['last_modified'].get(file_path)

        if cached_mtime is not None and abs(file_mtime - cached_mtime) < 1:
            return False

        current_hash = get_file_hash(file_path)
        if current_hash is None:
            return False

        cached_hash = _file_change_cache['file_hashes'].get(file_path)

        # 更新缓存
        _file_change_cache['file_hashes'][file_path] = current_hash
        _file_change_cache['last_modified'][file_path] = file_mtime
        _file_change_cache['last_check_time'][file_path] = time.time()

        if cached_hash is None or cached_hash != current_hash:
            logger.记录模块执行(f"检测到文件变化: {os.path.basename(file_path)}")
            return True

        return False

    except Exception as e:
        logger.记录错误(f"检查文件变化失败 {file_path}: {e}")
        return False

def _write_to_opening_price_table(trading_date, results):
    """写入基础数据A表"""
    import psycopg2
    try:
        # 使用内置数据库配置
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # 使用PostgreSQL的ON CONFLICT语法进行增量更新
        cursor.executemany('''
            INSERT INTO "基础数据A" (
                "日期", "股票代码", "开盘价", "收盘价", "最高价", "最低价",
                "涨停价", "跌停价", "真实换手", "更新时间戳"
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT ("日期", "股票代码") DO UPDATE SET
                "开盘价" = EXCLUDED."开盘价",
                "收盘价" = EXCLUDED."收盘价",
                "最高价" = EXCLUDED."最高价",
                "最低价" = EXCLUDED."最低价",
                "涨停价" = EXCLUDED."涨停价",
                "跌停价" = EXCLUDED."跌停价",
                "真实换手" = EXCLUDED."真实换手",
                "更新时间戳" = EXCLUDED."更新时间戳"
        ''', results)

        conn.commit()
        conn.close()

    except Exception as e:
        logger.记录错误("写入基础数据A表失败", e)

def log_heartbeat(success_count, fail_count, total_data):
    """心跳汇总 - 符合TradeFusion统一日志标准"""
    cache_info = get_trading_start_cache_info()
    cache_status = "缓存有效" if cache_info.get('cached', False) else "缓存失效"
    file_cache_count = len(_file_change_cache['file_hashes'])
    logger.记录模块执行(f"心跳汇总 - {success_count}次成功，{fail_count}次失败，{cache_status}，监控{file_cache_count}个文件", total_data)

def get_trading_start_cache_info():
    """获取缓存状态信息（用于调试和监控）"""
    global _trading_start_cache
    if (_trading_start_cache['timestamp'] is not None and
        _trading_start_cache['calculated_at'] is not None):
        cached_time_str = datetime.fromtimestamp(_trading_start_cache['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
        calculated_at_str = datetime.fromtimestamp(_trading_start_cache['calculated_at']).strftime('%Y-%m-%d %H:%M:%S')
        return {
            'cached': True,
            'trading_date': _trading_start_cache['trading_date'],
            'cached_time': cached_time_str,
            'calculated_at': calculated_at_str
        }
    else:
        return {'cached': False}

def parse_opening_price_data():
    """多字段数据解析函数"""
    try:
        trading_date = get_trading_date()
        date_str = str(trading_date)

        naive_date = datetime.strptime(date_str, "%Y%m%d")
        target_date = BEIJING_TZ.localize(naive_date.replace(hour=8, minute=0, second=0))
        target_timestamp = int(target_date.astimezone(pytz.utc).timestamp())

        results = []
        start_timestamp = get_trading_start_time()
        start_time_str = datetime.fromtimestamp(start_timestamp).strftime('%Y-%m-%d %H:%M:%S')
        logger.记录模块执行(f"时间过滤起点: {start_time_str}")

        # 增强目录读取监控
        file_dicts = {}
        for field_name, dir_path in DATA_DIRS.items():
            try:
                if os.path.exists(dir_path):
                    files = {os.path.splitext(f)[0]: f
                            for f in os.listdir(dir_path)
                            if f.endswith(".dat")}
                    file_dicts[field_name] = files
                    logger.记录模块执行(f"{field_name}目录: {len(files)}个文件")
                else:
                    logger.记录错误(f"{field_name}目录不存在: {dir_path}")
                    file_dicts[field_name] = {}
            except Exception as e:
                logger.记录错误(f"目录读取失败 {field_name}: {e}")
                file_dicts[field_name] = {}

        # 获取所有股票代码的并集（处理任何有数据的股票）
        all_stocks = set()
        for stocks in file_dicts.values():
            all_stocks.update(stocks.keys())

        if not all_stocks:
            return {
                'success': False,
                'data_count': 0,
                'error': 'No stock data found'
            }

        # 数据处理流程
        for stock_code in all_stocks:
            # 为每个数据类型创建文件路径（如果存在）
            file_paths = {}
            for field_name, files in file_dicts.items():
                if stock_code in files:
                    file_paths[field_name] = os.path.join(DATA_DIRS[field_name], files[stock_code])

            # 检查是否至少有一个数据文件
            if not file_paths:
                continue

            # 文件变化检测 - 只检查开盘价文件
            try:
                if '开盘价' not in file_paths:
                    continue

                # 检查开盘价文件是否发生变化（时间+内容）
                if not has_file_changed(file_paths['开盘价'], start_timestamp):
                    continue
            except Exception as e:
                continue

            # 数据解析逻辑
            data_values = {key: None for key in DATA_DIRS.keys()}  # 初始化所有字段为None

            for field_name, path in file_paths.items():
                try:
                    with open(path, "rb") as f:
                        data = f.read()
                        value = None
                        for i in range(0, len(data), 8):
                            if i+8 > len(data):
                                continue
                            timestamp = int.from_bytes(data[i:i+4], 'little')
                            if timestamp == target_timestamp:
                                value = struct.unpack('<f', data[i+4:i+8])[0]
                                break
                        data_values[field_name] = value
                except Exception as e:
                    pass

            # 至少需要开盘价数据才进行入库
            if data_values.get('开盘价') is not None:
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                results.append((
                    trading_date, stock_code,
                    data_values.get('开盘价'), data_values.get('收盘价'),
                    data_values.get('最高价'), data_values.get('最低价'),
                    data_values.get('涨停价'), data_values.get('跌停价'),
                    data_values.get('真实换手'), current_time
                ))

        # 记录采集成功状态
        logger.记录模块执行("多字段数据采集完成", len(results), "系统调度")

        # 只有当有数据时才写入数据库
        if results:
            _write_to_opening_price_table(trading_date, results)

        return {
            'success': True,
            'data_count': len(results),
            'results': results
        }

    except Exception as e:
        logger.记录错误(f"数据处理失败: {str(e)}")
        return {
            'success': False,
            'data_count': 0,
            'error': str(e)
        }

def run_continuous():
    """循环运行模式：每12秒执行一次"""
    global running, cycle_count, success_count, fail_count, total_data_processed

    # 程序启动时预热交易起始时间缓存
    logger.记录模块执行("程序启动，预热交易起始时间缓存...")
    try:
        get_trading_start_time()
        cache_info = get_trading_start_cache_info()
        if cache_info['cached']:
            logger.记录模块执行(f"缓存预热成功 - 交易日: {cache_info['trading_date']}, 起始时间: {cache_info['cached_time']}")
        else:
            logger.记录错误("缓存预热失败")
    except Exception as e:
        logger.记录错误(f"缓存预热异常: {e}")

    # 初始化文件变化检测缓存
    logger.记录模块执行("初始化文件变化检测缓存...")
    try:
        init_file_change_cache()
        logger.记录模块执行("文件变化检测缓存初始化完成")
    except Exception as e:
        logger.记录错误(f"文件变化检测缓存初始化失败: {e}")

    try:
        while running:
            cycle_count += 1
            start_time = time.time()

            # 检查是否在禁止运行时段
            if is_in_forbidden_time():
                now = get_current_beijing_time()
                logger.记录模块执行(f"当前时间{now.strftime('%H:%M')}在禁止运行时段(8:40~9:20)，跳过执行")

                if running:
                    time.sleep(12)
                continue

            try:
                # 执行数据处理
                result = parse_opening_price_data()

                if result and result.get('success', False):
                    success_count += 1
                    data_count = result.get('data_count', 0)
                    total_data_processed += data_count
                else:
                    fail_count += 1
                    error_msg = result.get('error', '未知错误') if result else '执行失败'
                    logger.记录错误(f"第{cycle_count}次执行失败: {error_msg}")

            except Exception as e:
                fail_count += 1
                logger.记录错误(f"第{cycle_count}次执行异常: {str(e)}")

            # 每10个周期输出一次心跳汇总
            if cycle_count % 10 == 0:
                log_heartbeat(success_count, fail_count, total_data_processed)

            # 如果还要继续运行，等待12秒
            if running:
                execution_time = time.time() - start_time
                sleep_time = max(0, 12 - execution_time)

                if sleep_time > 0:
                    time.sleep(sleep_time)
                else:
                    logger.记录错误(f"执行时间{execution_time:.1f}秒超过12秒间隔")

    except KeyboardInterrupt:
        logger.记录模块执行("用户中断，正在退出")
    except Exception as e:
        logger.记录错误(f"循环运行异常: {str(e)}")
    finally:
        # 保存文件变化检测缓存
        try:
            save_file_change_cache()
            logger.记录模块执行("文件变化检测缓存已保存")
        except Exception as e:
            logger.记录错误(f"保存文件变化检测缓存失败: {e}")

        logger.记录模块执行(f"循环结束汇总 - 运行{cycle_count}个周期，{success_count}次成功，{fail_count}次失败", total_data_processed)

if __name__ == "__main__":
    # 设置控制台编码
    import sys
    if sys.platform.startswith('win'):
        import os
        os.system('chcp 65001 > nul')  # 设置为UTF-8编码

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--once":
        # 单次执行模式
        try:
            init_file_change_cache()
        except Exception as e:
            logger.记录错误(f"文件变化检测缓存初始化失败: {e}")

        parse_opening_price_data()

        # 保存缓存
        try:
            save_file_change_cache()
        except Exception as e:
            logger.记录错误(f"保存文件变化检测缓存失败: {e}")
    else:
        # 循环执行模式（默认）
        run_continuous()
