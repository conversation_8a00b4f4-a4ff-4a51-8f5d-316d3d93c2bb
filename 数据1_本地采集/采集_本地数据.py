#!/usr/bin/env python
# -*- coding: utf-8 -*-
# {{ AURA-X: Restore - 恢复shebang行，双进程问题已通过替换python.exe解决. Approval: 寸止(ID:1738320024). }}

import os
import struct
import sys
import pytz
import time
import signal
import hashlib
import json
from pathlib import Path

# 重新设置项目根目录
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent  # 回退到TradeFusion目录

# 确保项目根目录在系统路径中
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    from 公共模块.交易日期 import get_trading_date
except ImportError as e:
    sys.exit(1)

from datetime import datetime

# {{ AURA-X: Add - 内置配置，解除对配置管理的依赖. Approval: 寸止(ID:1738320024). }}
# 内置数据目录配置
DATA_DIRS = {
    'lbgd': 'E:/dzh2/USERDATA/SelfData/10连板高度',
    'ztdp': 'E:/dzh2/USERDATA/SelfData/12涨停评分',
    'ztsj': 'E:/dzh2/USERDATA/SelfData/11涨停时间',
    'yzb': 'E:/dzh2/USERDATA/SelfData/13一字板',
    'tzb': 'E:/dzh2/USERDATA/SelfData/14T字板',
    'hjhs': 'E:/dzh2/USERDATA/SelfData/15黄金换手',
    'cjje': 'E:/dzh2/USERDATA/SelfData/22成交金额'
}

# 内置数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'tradefusion',
    'user': 'postgres',
    'password': 'ymjatTUU520'
}

# 内置处理配置
# {{ AURA-X: Delete - 删除固定20小时时间窗口配置，改用交易日期模块. Approval: 寸止(ID:1738320024). }}
# FILE_TIMEOUT_HOURS = 20  # 已删除，改用交易日期模块
FORBIDDEN_TIME_START = (8, 40)  # 8:40
FORBIDDEN_TIME_END = (9, 20)    # 9:20

# {{ AURA-X: Add - 全局时区对象，避免重复创建. Approval: 寸止(ID:1738320024). }}
# 全局时区对象，避免重复创建
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

# TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器

# 获取统一日志器
logger = 获取日志器("L1-本地采集")

# 循环控制变量
running = True
cycle_count = 0
success_count = 0
fail_count = 0
total_data_processed = 0

# {{ AURA-X: Add - 文件变化检测缓存机制. Approval: 寸止(ID:1738320024). }}
# 文件变化检测缓存
_file_change_cache = {
    'file_hashes': {},      # 文件路径 -> 哈希值
    'last_check_time': {},  # 文件路径 -> 最后检查时间
    'last_modified': {},    # 文件路径 -> 最后修改时间
    'cache_file': None      # 缓存文件路径
}

def signal_handler(signum, frame):
    """信号处理器：优雅退出"""
    global running
    logger.记录模块执行("接收到退出信号，正在优雅退出")
    logger.记录模块执行(f"最终汇总 - 运行{cycle_count}个周期，{success_count}次成功，{fail_count}次失败", total_data_processed)
    running = False

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# {{ AURA-X: Add - 添加时间检查函数，实现8:40~9:20禁止运行时段. Approval: 寸止(ID:1738320024). }}
def is_in_forbidden_time():
    """检查当前时间是否在禁止运行时段(8:40~9:20)"""
    # {{ AURA-X: Modify - 使用全局时区对象，避免重复创建. Approval: 寸止(ID:1738320024). }}
    now = datetime.now(BEIJING_TZ)
    current_time = (now.hour, now.minute)

    # 检查是否在禁止时段内
    return FORBIDDEN_TIME_START <= current_time < FORBIDDEN_TIME_END

def get_current_beijing_time():
    """获取当前北京时间，供其他函数复用"""
    return datetime.now(BEIJING_TZ)

def get_data_dirs():
    """获取数据目录配置"""
    return DATA_DIRS.copy()

# {{ AURA-X: Add - 添加交易起始时间缓存机制，避免重复计算. Approval: 寸止(ID:1738320024). }}
# 交易起始时间缓存
_trading_start_cache = {
    'timestamp': None,
    'trading_date': None,
    'calculated_at': None
}

def get_trading_start_time():
    """
    获取基于交易日期的起始时间点（带缓存优化）
    返回: start_timestamp - 基准日期的上午9:20的时间戳
    """
    global _trading_start_cache

    try:
        # 获取当前交易日期
        current_trading_date = get_trading_date()

        # 检查缓存是否有效
        if (_trading_start_cache['timestamp'] is not None and
            _trading_start_cache['trading_date'] == current_trading_date):
            # 使用缓存值
            logger.记录模块执行(f"使用缓存的交易起始时间 (交易日: {current_trading_date})")
            return _trading_start_cache['timestamp']

        # 缓存失效或首次计算，重新计算
        logger.记录模块执行(f"重新计算交易起始时间 (交易日: {current_trading_date})")

        date_str = str(current_trading_date)

        # 解析交易日期并设置为上午9:20
        naive_date = datetime.strptime(date_str, "%Y%m%d")
        start_time = BEIJING_TZ.localize(naive_date.replace(hour=9, minute=20, second=0))
        start_timestamp = start_time.timestamp()

        # 更新缓存
        _trading_start_cache = {
            'timestamp': start_timestamp,
            'trading_date': current_trading_date,
            'calculated_at': time.time()
        }

        return start_timestamp

    except Exception as e:
        logger.记录错误(f"获取交易起始时间失败: {e}")
        # 降级处理：使用当前时间前20小时作为备用方案
        current_timestamp = time.time()
        fallback_start = current_timestamp - (20 * 3600)  # 20小时前

        # 清空缓存以防止错误数据被缓存
        _trading_start_cache = {'timestamp': None, 'trading_date': None, 'calculated_at': None}

        return fallback_start

def clear_trading_start_cache():
    """
    清理交易起始时间缓存
    用于手动刷新或程序重启时调用
    """
    global _trading_start_cache
    _trading_start_cache = {'timestamp': None, 'trading_date': None, 'calculated_at': None}
    logger.记录模块执行("交易起始时间缓存已清理")

def get_trading_start_cache_info():
    """
    获取缓存状态信息（用于调试和监控）
    """
    global _trading_start_cache
    if (_trading_start_cache['timestamp'] is not None and
        _trading_start_cache['calculated_at'] is not None):
        cached_time_str = datetime.fromtimestamp(_trading_start_cache['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
        calculated_at_str = datetime.fromtimestamp(_trading_start_cache['calculated_at']).strftime('%Y-%m-%d %H:%M:%S')
        return {
            'cached': True,
            'trading_date': _trading_start_cache['trading_date'],
            'cached_time': cached_time_str,
            'calculated_at': calculated_at_str
        }
    else:
        return {'cached': False}

def is_file_modified_after_trading_start(file_path, start_timestamp):
    """
    检查文件是否在交易起始时间之后被修改
    参数:
        file_path - 文件路径
        start_timestamp - 起始时间戳
    返回: True 如果文件在起始时间之后被修改，False 否则
    """
    try:
        # 获取文件修改时间
        file_mtime = os.path.getmtime(file_path)

        # {{ AURA-X: Modify - 简化为只检查文件是否在起始时间之后被修改. Approval: 寸止(ID:1738320024). }}
        # 检查文件修改时间是否在起始时间之后
        return file_mtime >= start_timestamp

    except Exception as e:
        logger.记录错误(f"检查文件修改时间失败 {file_path}: {e}")
        return False

# {{ AURA-X: Add - 文件变化检测核心函数. Approval: 寸止(ID:1738320024). }}
def init_file_change_cache():
    """初始化文件变化检测缓存"""
    global _file_change_cache

    # 设置缓存文件路径
    cache_dir = Path(__file__).parent / '.cache'
    cache_dir.mkdir(exist_ok=True)
    _file_change_cache['cache_file'] = cache_dir / 'file_change_cache.json'

    # 尝试加载已有缓存
    try:
        if _file_change_cache['cache_file'].exists():
            with open(_file_change_cache['cache_file'], 'r', encoding='utf-8') as f:
                cached_data = json.load(f)
                _file_change_cache['file_hashes'] = cached_data.get('file_hashes', {})
                _file_change_cache['last_check_time'] = cached_data.get('last_check_time', {})
                _file_change_cache['last_modified'] = cached_data.get('last_modified', {})
            logger.记录模块执行(f"文件变化缓存加载成功，包含{len(_file_change_cache['file_hashes'])}个文件记录")
    except Exception as e:
        logger.记录错误(f"加载文件变化缓存失败: {e}")
        # 重置缓存
        _file_change_cache['file_hashes'] = {}
        _file_change_cache['last_check_time'] = {}
        _file_change_cache['last_modified'] = {}

def save_file_change_cache():
    """保存文件变化检测缓存"""
    global _file_change_cache

    try:
        cache_data = {
            'file_hashes': _file_change_cache['file_hashes'],
            'last_check_time': _file_change_cache['last_check_time'],
            'last_modified': _file_change_cache['last_modified']
        }
        with open(_file_change_cache['cache_file'], 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.记录错误(f"保存文件变化缓存失败: {e}")

def get_file_hash(file_path):
    """
    计算DAT文件中当前交易日数据的哈希值
    专门针对DAT文件格式：只检测target_timestamp对应的数值变化
    """
    try:
        # 获取当前交易日的target_timestamp
        trading_date = get_trading_date()
        date_str = str(trading_date)
        naive_date = datetime.strptime(date_str, "%Y%m%d")
        target_date = BEIJING_TZ.localize(naive_date.replace(hour=8, minute=0, second=0))
        target_timestamp = int(target_date.astimezone(pytz.utc).timestamp())

        # 在DAT文件中查找target_timestamp对应的数值
        target_value = None
        with open(file_path, "rb") as f:
            while True:
                record = f.read(8)
                if not record or len(record) != 8:
                    break

                # 解析8字节记录：4字节时间戳 + 4字节浮点数
                timestamp = int.from_bytes(record[:4], 'little')
                if timestamp == target_timestamp:
                    target_value = struct.unpack('<f', record[4:])[0]
                    break

        # 如果找到目标数据，计算其哈希值
        if target_value is not None:
            # 使用时间戳和数值组合计算哈希，确保精确检测数值变化
            hash_data = f"{target_timestamp}:{target_value}".encode('utf-8')
            return hashlib.md5(hash_data).hexdigest()
        else:
            # 如果没有找到目标时间戳的数据，返回特殊标识
            return f"no_data_{target_timestamp}"

    except Exception as e:
        logger.记录错误(f"计算DAT文件哈希失败 {file_path}: {e}")
        return None

def has_file_changed(file_path, start_timestamp):
    """
    检查文件是否发生变化（结合修改时间和内容哈希）
    参数:
        file_path - 文件路径
        start_timestamp - 起始时间戳
    返回: True 如果文件发生变化，False 否则
    """
    global _file_change_cache

    try:
        # 首先检查文件是否存在
        if not os.path.exists(file_path):
            return False

        # 获取文件修改时间
        file_mtime = os.path.getmtime(file_path)

        # 如果文件修改时间早于起始时间，直接返回False
        if file_mtime < start_timestamp:
            return False

        # 检查缓存中的修改时间
        cached_mtime = _file_change_cache['last_modified'].get(file_path)

        # 如果修改时间没有变化，认为文件未变化
        if cached_mtime is not None and abs(file_mtime - cached_mtime) < 1:  # 1秒容差
            return False

        # 修改时间发生变化，进一步检查文件内容哈希
        current_hash = get_file_hash(file_path)
        if current_hash is None:
            return False

        cached_hash = _file_change_cache['file_hashes'].get(file_path)

        # 更新缓存
        _file_change_cache['file_hashes'][file_path] = current_hash
        _file_change_cache['last_modified'][file_path] = file_mtime
        _file_change_cache['last_check_time'][file_path] = time.time()

        # 如果哈希值发生变化，说明文件内容确实变化了
        if cached_hash is None or cached_hash != current_hash:
            logger.记录模块执行(f"检测到文件变化: {os.path.basename(file_path)}")
            return True

        return False

    except Exception as e:
        logger.记录错误(f"检查文件变化失败 {file_path}: {e}")
        return False

# {{ AURA-X: Delete - 删除冗余的日志包装函数，直接使用logger. Approval: 寸止(ID:1738320024). }}
# 删除log_success和log_error函数，直接使用logger

def _write_to_height_table(trading_date, results):
    """写入个股连板高度表，并自动触发后续处理模块"""
    # {{ AURA-X: Modify - 将SQLite连接改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
    import psycopg2
    try:
        # {{ AURA-X: Modify - 使用内置数据库配置，解除对配置管理的依赖. Approval: 寸止(ID:1738320024). }}
        # 使用内置数据库配置
        conn = psycopg2.connect(**DB_CONFIG)

        # 根据依赖关系分析，本模块应直接写入个股连板高度表
        # 使用UPSERT模式：增量更新，避免删除未变化的数据
        # {{ AURA-X: Modify - 改为UPSERT模式，只更新变化的股票数据. Approval: 寸止(ID:1738320024). }}
        cursor = conn.cursor()

        # 使用PostgreSQL的ON CONFLICT语法进行增量更新
        cursor.executemany('''
            INSERT INTO "个股连板高度表" (
                "日期", "股票代码", "连板高度", "涨停评分", "涨停时间",
                "一字板", "T字板", "黄金换手", "成交金额"
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT ("日期", "股票代码") DO UPDATE SET
                "连板高度" = EXCLUDED."连板高度",
                "涨停评分" = EXCLUDED."涨停评分",
                "涨停时间" = EXCLUDED."涨停时间",
                "一字板" = EXCLUDED."一字板",
                "T字板" = EXCLUDED."T字板",
                "黄金换手" = EXCLUDED."黄金换手",
                "成交金额" = EXCLUDED."成交金额"
        ''', results)

        conn.commit()
        conn.close()

        # 🔄 个股连板高度表写入完成后，自动触发第2层数据处理模块
        _trigger_layer2_processing()

    except Exception as e:
        logger.记录错误("写入个股连板高度表失败", e)

def _trigger_layer2_processing():
    """触发第2层数据处理模块（板块涨停表）"""
    try:
        # 导入第2层数据处理模块
        from 数据库2_板块层统计.板块涨停表 import main as 板块涨停_main

        logger.记录模块执行("自动触发第2层数据处理模块")

        # 调用板块涨停表处理函数
        result = 板块涨停_main()

        if result:
            logger.记录模块执行("第2层数据处理模块执行成功")
        else:
            logger.记录错误("第2层数据处理模块执行失败")

    except Exception as e:
        logger.记录错误("触发第2层数据处理模块异常", e)


def log_heartbeat(success_count, fail_count, total_data):
    """心跳汇总 - 符合TradeFusion统一日志标准"""
    # {{ AURA-X: Add - 在心跳汇总中添加缓存状态监控. Approval: 寸止(ID:1738320024). }}
    cache_info = get_trading_start_cache_info()
    cache_status = "缓存有效" if cache_info['cached'] else "缓存失效"

    # {{ AURA-X: Add - 在心跳汇总中添加文件变化检测统计. Approval: 寸止(ID:1738320024). }}
    file_cache_count = len(_file_change_cache['file_hashes'])
    logger.记录模块执行(f"心跳汇总 - {success_count}次成功，{fail_count}次失败，{cache_status}，监控{file_cache_count}个文件", total_data)

def parse_lbgd_data():
    """带日志记录的本地数据解析函数"""
    # {{ AURA-X: Modify - 移除不必要的内嵌函数，简化调用层级. Approval: 寸止(ID:1738320024). }}
    try:
        trading_date = get_trading_date()
        date_str = str(trading_date)

        # {{ AURA-X: Modify - 使用全局时区对象，避免重复创建. Approval: 寸止(ID:1738320024). }}
        naive_date = datetime.strptime(date_str, "%Y%m%d")
        target_date = BEIJING_TZ.localize(naive_date.replace(hour=8,  minute=0, second=0))
        target_timestamp = int(target_date.astimezone(pytz.utc).timestamp())

        # {{ AURA-X: Modify - 使用内置配置函数，解除对配置管理的依赖. Approval: 寸止(ID:1738320024). }}
        # 获取数据目录配置
        data_dirs = get_data_dirs()

        results = []
        # {{ AURA-X: Modify - 简化为只计算起始时间点，减少计算复杂度. Approval: 寸止(ID:1738320024). }}
        # 计算交易起始时间（只计算一次）
        start_timestamp = get_trading_start_time()
        start_time_str = datetime.fromtimestamp(start_timestamp).strftime('%Y-%m-%d %H:%M:%S')
        logger.记录模块执行(f"时间过滤起点: {start_time_str}")

        # 增强目录读取监控
        file_dicts = {}
        for dir_type, dir_path in data_dirs.items():
            try:
                files = {os.path.splitext(f)[0]: f
                        for f in os.listdir(dir_path)
                        if f.endswith(".dat")}
                file_dicts[dir_type] = files
            except Exception as e:
                logger.记录错误(f"目录读取失败 {dir_type}: {e}")
                file_dicts[dir_type] = {}

        # 获取所有股票代码的并集（处理任何有数据的股票）
        all_stocks = set()
        for stocks in file_dicts.values():
            all_stocks.update(stocks.keys())

        if not all_stocks:
            return {
                'success': False,
                'data_count': 0,
                'error': 'No stock data found'
            }

        # 数据处理流程
        for stock_code in all_stocks:
            # 为每个数据类型创建文件路径（如果存在）
            file_paths = {}
            for dir_type, files in file_dicts.items():
                if stock_code in files:
                    file_paths[dir_type] = os.path.join(data_dirs[dir_type], files[stock_code])

            # 检查是否至少有一个数据文件
            if not file_paths:
                continue

            # {{ AURA-X: Modify - 使用新的文件变化检测机制替换旧的时效性检查. Approval: 寸止(ID:1738320024). }}
            # 文件变化检测 - 只检查连板高度文件
            try:
                # 检查连板高度文件是否存在
                if 'lbgd' not in file_paths:
                    continue

                # {{ AURA-X: Modify - 使用新的文件变化检测，结合修改时间和内容哈希. Approval: 寸止(ID:1738320024). }}
                # 检查连板高度文件是否发生变化（时间+内容）
                if not has_file_changed(file_paths['lbgd'], start_timestamp):
                    continue
            except Exception as e:
                continue

            # 数据解析逻辑
            data_values = {key: None for key in data_dirs.keys()}  # 初始化所有字段为None

            for key, path in file_paths.items():
                try:
                    with open(path, "rb") as f:
                        data = f.read()
                        value = None
                        for i in range(0, len(data), 8):
                            if i+8 > len(data):
                                continue
                            timestamp = int.from_bytes(data[i:i+4], 'little')
                            if timestamp == target_timestamp:
                                value = struct.unpack('<f', data[i+4:i+8])[0]
                                break
                        data_values[key] = value
                except Exception as e:
                    pass

            # 至少需要连板高度数据才进行入库
            if data_values.get('lbgd') is not None:
                results.append((
                    trading_date, stock_code,
                    data_values.get('lbgd'), data_values.get('ztdp'),
                    data_values.get('ztsj'), data_values.get('yzb'),
                    data_values.get('tzb'), data_values.get('hjhs'),
                    data_values.get('cjje')
                ))

        # 记录采集成功状态
        logger.记录模块执行("本地数据采集完成", len(results), "系统调度")

        # 只有当有数据时才写入数据库并触发下游模块
        if results:
            # 写入个股连板高度表，并自动触发后续处理模块
            _write_to_height_table(trading_date, results)
        # 如果没有数据，不触发下游模块，避免无意义的处理

        return {
            'success': True,
            'data_count': len(results),
            'results': results
        }

    except Exception as e:
        # 按设计方案记录错误状态
        logger.记录错误(f"数据处理失败: {str(e)}")
        return {
            'success': False,
            'data_count': 0,
            'error': str(e)
        }

# {{ AURA-X: Delete - 删除未使用的process_lbgd_data函数. Approval: 寸止(ID:1738320024). }}
# 删除process_lbgd_data函数，未被使用

def run_continuous():
    """循环运行模式：每12秒执行一次"""
    global running, cycle_count, success_count, fail_count, total_data_processed

    # {{ AURA-X: Add - 程序启动时预热缓存，提高首次执行性能. Approval: 寸止(ID:1738320024). }}
    # 程序启动时预热交易起始时间缓存
    logger.记录模块执行("程序启动，预热交易起始时间缓存...")
    try:
        # 预热缓存
        get_trading_start_time()
        cache_info = get_trading_start_cache_info()
        if cache_info['cached']:
            logger.记录模块执行(f"缓存预热成功 - 交易日: {cache_info['trading_date']}, 起始时间: {cache_info['cached_time']}")
        else:
            logger.记录错误("缓存预热失败")
    except Exception as e:
        logger.记录错误(f"缓存预热异常: {e}")

    # {{ AURA-X: Add - 初始化文件变化检测缓存. Approval: 寸止(ID:1738320024). }}
    # 初始化文件变化检测缓存
    logger.记录模块执行("初始化文件变化检测缓存...")
    try:
        init_file_change_cache()
        logger.记录模块执行("文件变化检测缓存初始化完成")
    except Exception as e:
        logger.记录错误(f"文件变化检测缓存初始化失败: {e}")

    # 移除开始日志，只保留心跳汇总

    try:
        while running:
            cycle_count += 1
            start_time = time.time()

            # {{ AURA-X: Add - 添加禁止时段检查，8:40~9:20时段跳过执行. Approval: 寸止(ID:1738320024). }}
            # 检查是否在禁止运行时段
            if is_in_forbidden_time():
                # {{ AURA-X: Modify - 使用全局时间获取函数，避免重复创建时区对象. Approval: 寸止(ID:1738320024). }}
                now = get_current_beijing_time()
                logger.记录模块执行(f"当前时间{now.strftime('%H:%M')}在禁止运行时段(8:40~9:20)，跳过执行")

                # 等待12秒后继续检查
                if running:
                    time.sleep(12)
                continue

            try:
                # 执行数据处理
                result = parse_lbgd_data()

                if result and result.get('success', False):
                    success_count += 1
                    data_count = result.get('data_count', 0)
                    total_data_processed += data_count
                    # 移除每次执行成功日志，只保留心跳汇总
                else:
                    fail_count += 1
                    error_msg = result.get('error', '未知错误') if result else '执行失败'
                    logger.记录错误(f"第{cycle_count}次执行失败: {error_msg}")

            except Exception as e:
                fail_count += 1
                logger.记录错误(f"第{cycle_count}次执行异常: {str(e)}")

            # 每10个周期输出一次心跳汇总
            if cycle_count % 10 == 0:
                log_heartbeat(success_count, fail_count, total_data_processed)

            # 如果还要继续运行，等待12秒
            if running:
                execution_time = time.time() - start_time
                sleep_time = max(0, 12 - execution_time)

                if sleep_time > 0:
                    # 移除等待日志
                    time.sleep(sleep_time)
                else:
                    logger.记录错误(f"执行时间{execution_time:.1f}秒超过12秒间隔")

    except KeyboardInterrupt:
        logger.记录模块执行("用户中断，正在退出")
    except Exception as e:
        logger.记录错误(f"循环运行异常: {str(e)}")
    finally:
        # {{ AURA-X: Add - 程序退出时保存文件变化检测缓存. Approval: 寸止(ID:1738320024). }}
        # 保存文件变化检测缓存
        try:
            save_file_change_cache()
            logger.记录模块执行("文件变化检测缓存已保存")
        except Exception as e:
            logger.记录错误(f"保存文件变化检测缓存失败: {e}")

        logger.记录模块执行(f"循环结束汇总 - 运行{cycle_count}个周期，{success_count}次成功，{fail_count}次失败", total_data_processed)

if __name__ == "__main__":
    # 设置控制台编码
    import sys
    if sys.platform.startswith('win'):
        import os
        os.system('chcp 65001 > nul')  # 设置为UTF-8编码

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--once":
        # 单次执行模式
        # {{ AURA-X: Add - 单次执行模式也需要初始化文件变化检测缓存. Approval: 寸止(ID:1738320024). }}
        try:
            init_file_change_cache()
        except Exception as e:
            logger.记录错误(f"文件变化检测缓存初始化失败: {e}")

        # 移除单次执行开始日志
        parse_lbgd_data()

        # 保存缓存
        try:
            save_file_change_cache()
        except Exception as e:
            logger.记录错误(f"保存文件变化检测缓存失败: {e}")
    else:
        # 循环执行模式（默认）
        run_continuous()