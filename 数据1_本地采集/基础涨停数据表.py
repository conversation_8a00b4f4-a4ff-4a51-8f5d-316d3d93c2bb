# {{ AURA-X: Modify - 将SQLite连接改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
# b_sjk.py 修改版
import psycopg2
import psycopg2.extras
import sys
import logging
from pathlib import Path
from typing import List, Optional

project_root = Path(__file__).absolute().parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

# TradeFusion数据流日志系统 - 低频模块
from logging.handlers import TimedRotatingFileHandler
import os

class ConsoleColorFormatter(logging.Formatter):
    """控制台颜色格式器：只在控制台显示时添加颜色"""
    def format(self, record):
        formatted = super().format(record)
        # 为控制台添加颜色
        if '<基础涨停数据表>' in formatted:
            formatted = formatted.replace('<基础涨停数据表>', '<\033[94m基础涨停数据表\033[0m>')
        if '[个股连板高度表]' in formatted:
            formatted = formatted.replace('[个股连板高度表]', '[\033[92m个股连板高度表\033[0m]')
        if '<采集_本地数据>' in formatted:
            formatted = formatted.replace('<采集_本地数据>', '<\033[94m采集_本地数据\033[0m>')
        if '✗' in formatted:
            formatted = formatted.replace('✗', '\033[91m✗\033[0m')
        return formatted

# 设置低频模块专用日志格式
logger = logging.getLogger('基础涨停数据表')
logger.handlers.clear()  # 清除现有handlers避免重复

# 控制台输出 - 带颜色
console_handler = logging.StreamHandler()
console_formatter = ConsoleColorFormatter('[%(asctime)s] %(message)s', datefmt='%H:%M:%S')
console_handler.setFormatter(console_formatter)
logger.addHandler(console_handler)

# 文件存储 - 纯文本，保留7天（低频模块）
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
file_handler = TimedRotatingFileHandler(
    filename=os.path.join(log_dir, '基础涨停数据表.log'),
    when='midnight',
    interval=1,
    backupCount=7,  # 保留7天
    encoding='utf-8'
)
file_formatter = logging.Formatter('[%(asctime)s] %(message)s', datefmt='%H:%M:%S')
file_handler.setFormatter(file_formatter)
logger.addHandler(file_handler)

logger.setLevel(logging.INFO)
logger.propagate = False  # 防止向上级logger传播

# TradeFusion智能日志系统 - 稍后从全局角度重新设计

class HeightDatabase:
    def __init__(self, db_config: Optional[dict] = None):
        # {{ AURA-X: Modify - 使用统一配置管理而非硬编码. Approval: 寸止(ID:1737734400). }}
        if db_config is None:
            try:
                from 公共模块.配置管理 import get_config
                config = get_config()
                self.db_config = config.get('database')
                if not self.db_config:
                    raise ValueError("数据库配置为空")
            except ImportError:
                # 备用配置
                self.db_config = {
                    'host': 'localhost',
                    'port': 5432,
                    'database': 'tradefusion',
                    'user': 'postgres',
                    'password': 'ymjatTUU520'
                }
        else:
            self.db_config = db_config
        self.conn = None
        self._connect()
        self._add_column_if_not_exists()

    def _add_column_if_not_exists(self):
        # {{ AURA-X: Modify - 修改为PostgreSQL语法，添加连接检查. Approval: 寸止(ID:1737734400). }}
        if not self.conn:
            raise RuntimeError("数据库连接未建立")

        cursor = self.conn.cursor()
        try:
            # PostgreSQL检查列是否存在的方法
            cursor.execute("""
                SELECT column_name FROM information_schema.columns
                WHERE table_name = '个股连板高度表'
            """)
            columns = [column[0] for column in cursor.fetchall()]
            for column in ['涨停评分', '涨停时间', '一字板', 'T字板', '黄金换手', '成交金额']:
                if column not in columns:
                    cursor.execute(f'ALTER TABLE "个股连板高度表" ADD COLUMN "{column}" REAL')
            self.conn.commit()
        except Exception as e:
            raise
        finally:
            cursor.close()

    def _connect(self):
        # {{ AURA-X: Modify - 修改为PostgreSQL连接方法，添加错误处理. Approval: 寸止(ID:1737734400). }}
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.conn.autocommit = False
            logger.info(f"🔌 <基础涨停数据表> PostgreSQL连接成功")
        except Exception as e:
            logger.error(f"❌ <基础涨停数据表> 数据库连接失败: {e}")
            self.conn = None
            raise

    def _create_temp_table(self, lb_data: List[tuple]):
        # {{ AURA-X: Modify - 修改为PostgreSQL语法，添加连接检查. Approval: 寸止(ID:1737734400). }}
        if not self.conn:
            raise RuntimeError("数据库连接未建立")

        try:
            cursor = self.conn.cursor()
            try:
                cursor.execute("""
                    CREATE TEMP TABLE IF NOT EXISTS temp_lb_data (
                        "日期" INTEGER,
                        "股票代码" VARCHAR(8),
                        "连板高度" REAL,
                        "涨停评分" REAL,
                        "涨停时间" REAL,
                        "一字板" REAL,
                        "T字板" REAL,
                        "黄金换手" REAL,
                        "成交金额" REAL,
                        PRIMARY KEY ("日期", "股票代码")
                    )""")
                cursor.execute("DELETE FROM temp_lb_data")
                cursor.executemany(
                    "INSERT INTO temp_lb_data VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)",
                    lb_data
                )
                self.conn.commit()
            finally:
                cursor.close()
        except Exception as e:
            raise

    def _update_height(self) -> int:
        # {{ AURA-X: Modify - 修改为PostgreSQL cursor方式，添加连接检查. Approval: 寸止(ID:1737734400). }}
        if not self.conn:
            raise RuntimeError("数据库连接未建立")

        try:
            cursor = self.conn.cursor()
            try:
                # 更新现有记录
                cursor.execute("""
                    UPDATE "个股连板高度表"
                    SET
                        "连板高度" = (SELECT "连板高度" FROM temp_lb_data WHERE "个股连板高度表"."日期" = temp_lb_data."日期" AND "个股连板高度表"."股票代码" = temp_lb_data."股票代码"),
                        "涨停评分" = (SELECT "涨停评分" FROM temp_lb_data WHERE "个股连板高度表"."日期" = temp_lb_data."日期" AND "个股连板高度表"."股票代码" = temp_lb_data."股票代码"),
                        "涨停时间" = (SELECT "涨停时间" FROM temp_lb_data WHERE "个股连板高度表"."日期" = temp_lb_data."日期" AND "个股连板高度表"."股票代码" = temp_lb_data."股票代码"),
                        "一字板" = (SELECT "一字板" FROM temp_lb_data WHERE "个股连板高度表"."日期" = temp_lb_data."日期" AND "个股连板高度表"."股票代码" = temp_lb_data."股票代码"),
                        "T字板" = (SELECT "T字板" FROM temp_lb_data WHERE "个股连板高度表"."日期" = temp_lb_data."日期" AND "个股连板高度表"."股票代码" = temp_lb_data."股票代码"),
                        "黄金换手" = (SELECT "黄金换手" FROM temp_lb_data WHERE "个股连板高度表"."日期" = temp_lb_data."日期" AND "个股连板高度表"."股票代码" = temp_lb_data."股票代码"),
                        "成交金额" = (SELECT "成交金额" FROM temp_lb_data WHERE "个股连板高度表"."日期" = temp_lb_data."日期" AND "个股连板高度表"."股票代码" = temp_lb_data."股票代码")
                    WHERE EXISTS (
                        SELECT 1 FROM temp_lb_data
                        WHERE "个股连板高度表"."日期" = temp_lb_data."日期" AND "个股连板高度表"."股票代码" = temp_lb_data."股票代码"
                    )""")
                updated_rows = cursor.rowcount

                # 插入新记录
                cursor.execute("""
                    INSERT INTO "个股连板高度表" ("日期", "股票代码", "连板高度", "涨停评分", "涨停时间", "一字板", "T字板", "黄金换手", "成交金额")
                    SELECT "日期", "股票代码", "连板高度", "涨停评分", "涨停时间", "一字板", "T字板", "黄金换手", "成交金额"
                    FROM temp_lb_data
                    WHERE NOT EXISTS (
                        SELECT 1 FROM "个股连板高度表"
                        WHERE "个股连板高度表"."日期" = temp_lb_data."日期" AND "个股连板高度表"."股票代码" = temp_lb_data."股票代码"
                    )""")
                inserted_rows = cursor.rowcount

                self.conn.commit()
                return updated_rows + inserted_rows
            finally:
                cursor.close()
        except Exception as e:
            logger.error(f"❌ \033[91m[基础涨停数据表]\033[0m 数据更新失败: {str(e)}")
            raise

    def update_height(self, date: int, lb_data: List[tuple]) -> int:
        # {{ AURA-X: Modify - 添加连接检查和日期参数使用. Approval: 寸止(ID:1737734400). }}
        if not self.conn:
            raise RuntimeError("数据库连接未建立")

        try:
            self._create_temp_table(lb_data)
            affected_rows = self._update_height()
            logger.info(f"\033[91m[基础涨停数据表]\033[0m 连板高度更新完成 - \033[93m个股连板高度表\033[0m+\033[92m{affected_rows:,}条\033[0m (日期:{date}, 由\033[91m[采集_本地数据]\033[0m调用)")
            return affected_rows
        except Exception as e:
            logger.error(f"✗ <基础涨停数据表> 更新流程异常: {str(e)}")
            raise

    def __del__(self):
        if self.conn:
            try:
                self.conn.close()
            except Exception as e:
                pass

if __name__ == "__main__":
    db = HeightDatabase()
    # {{ AURA-X: Modify - 修正测试数据字段数量，匹配9个字段. Approval: 寸止(ID:1737734400). }}
    sample_data = [(20230308, '600000', 3.0, 8.5, 9.2, 1.0, 0.0, 0.5, 1000000.0)]
    db.update_height(20230308, sample_data)