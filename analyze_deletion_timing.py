#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析删除时机可能引发的风险
"""
import psycopg2
from datetime import datetime, timedelta

def analyze_deletion_timing():
    # 连接数据库
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'tradefusion',
        'user': 'postgres',
        'password': 'ymjatTUU520'
    }
    
    try:
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        
        print("=== 删除时机风险分析 ===\n")
        
        # 查询最新日期
        cursor.execute('SELECT MAX("日期") FROM "个股人气表"')
        max_date = cursor.fetchone()[0]
        print(f'分析日期: {max_date}\n')
        
        # 1. 分析删除时机的执行顺序
        print("🔄 执行顺序分析:")
        print("   1. 同花采集 → 同花采集完成后处理() → 不删除记录")
        print("   2. 东财采集 → 东财采集完成后处理() → 删除低质量记录")
        print("   3. 删除条件: 综合人气评分 = 0 OR NULL\n")
        
        # 2. 模拟不同执行顺序的风险场景
        print("⚠️ 风险场景分析:\n")
        
        # 场景1: 只有同花数据时的状态
        print("📊 场景1: 只有同花数据时")
        cursor.execute('''
            SELECT COUNT(*) FROM "个股人气表" 
            WHERE "日期" = %s 
            AND "同花人气排名" IS NOT NULL 
            AND "东财人气排名" IS NULL
        ''', (max_date,))
        only_ths_count = cursor.fetchone()[0]
        print(f"   只有同花数据的记录数: {only_ths_count}")
        
        if only_ths_count > 0:
            cursor.execute('''
                SELECT COUNT(*) FROM "个股人气表" 
                WHERE "日期" = %s 
                AND "同花人气排名" IS NOT NULL 
                AND "东财人气排名" IS NULL
                AND ("综合人气评分" = 0 OR "综合人气评分" IS NULL)
            ''', (max_date,))
            ths_zero_score = cursor.fetchone()[0]
            print(f"   其中综合评分为0的: {ths_zero_score} (会被删除)")
            print(f"   风险: 如果东财采集在同花之后运行，这些记录会被删除")
        
        # 场景2: 只有东财数据时的状态
        print(f"\n📊 场景2: 只有东财数据时")
        cursor.execute('''
            SELECT COUNT(*) FROM "个股人气表" 
            WHERE "日期" = %s 
            AND "东财人气排名" IS NOT NULL 
            AND "同花人气排名" IS NULL
        ''', (max_date,))
        only_dcf_count = cursor.fetchone()[0]
        print(f"   只有东财数据的记录数: {only_dcf_count}")
        
        if only_dcf_count > 0:
            cursor.execute('''
                SELECT COUNT(*) FROM "个股人气表" 
                WHERE "日期" = %s 
                AND "东财人气排名" IS NOT NULL 
                AND "同花人气排名" IS NULL
                AND ("综合人气评分" = 0 OR "综合人气评分" IS NULL)
            ''', (max_date,))
            dcf_zero_score = cursor.fetchone()[0]
            print(f"   其中综合评分为0的: {dcf_zero_score} (会被删除)")
            print(f"   风险: 如果同花采集失败，这些记录会被删除")
        
        # 3. 分析历史数据的删除风险
        print(f"\n📊 场景3: 历史数据保护分析")
        
        # 查询前一天的数据
        yesterday = int((datetime.strptime(str(max_date), '%Y%m%d') - timedelta(days=1)).strftime('%Y%m%d'))
        cursor.execute('SELECT COUNT(*) FROM "个股人气表" WHERE "日期" = %s', (yesterday,))
        result = cursor.fetchone()
        yesterday_count = result[0] if result else 0
        print(f"   前一天({yesterday})记录数: {yesterday_count}")
        
        if yesterday_count > 0:
            cursor.execute('''
                SELECT COUNT(*) FROM "个股人气表" 
                WHERE "日期" = %s AND "综合人气评分" > 0
            ''', (yesterday,))
            yesterday_valid = cursor.fetchone()[0]
            print(f"   前一天有效记录数: {yesterday_valid}")
            print(f"   风险: 删除操作只针对当日，历史数据安全")
        
        # 4. 分析删除操作的原子性
        print(f"\n🔒 原子性分析:")
        print("   删除操作在事务中执行，具有原子性")
        print("   但不同采集模块的执行时机可能导致数据丢失")
        
        # 5. 分析时间窗口风险
        print(f"\n⏰ 时间窗口风险分析:")
        
        # 查询更新时间戳分布
        cursor.execute('''
            SELECT 
                MIN("更新时间戳") as min_time,
                MAX("更新时间戳") as max_time,
                COUNT(DISTINCT "更新时间戳") as unique_timestamps
            FROM "个股人气表" 
            WHERE "日期" = %s
        ''', (max_date,))
        time_stats = cursor.fetchone()
        
        if time_stats and time_stats[0]:
            min_time, max_time, unique_timestamps = time_stats
            print(f"   最早更新时间: {min_time}")
            print(f"   最晚更新时间: {max_time}")
            print(f"   不同时间戳数量: {unique_timestamps}")
            
            # 计算时间跨度
            if min_time and max_time:
                time_span = max_time - min_time
                print(f"   更新时间跨度: {time_span}")
                
                if time_span.total_seconds() > 300:  # 5分钟
                    print("   ⚠️ 警告: 更新时间跨度较大，存在时间窗口风险")
        
        # 6. 提出风险缓解建议
        print(f"\n💡 风险缓解建议:")
        print("   1. 确保删除操作只在所有数据源都更新完成后执行")
        print("   2. 考虑使用标志位标记数据更新完成状态")
        print("   3. 实现数据源依赖检查机制")
        print("   4. 添加删除前的数据完整性验证")
        print("   5. 考虑软删除机制，而非硬删除")
        
        # 7. 检查当前的保护机制
        print(f"\n🛡️ 当前保护机制:")
        print("   ✅ 同花采集完成后不删除记录")
        print("   ✅ 只有东财采集完成后才删除记录")
        print("   ✅ 删除操作在事务中执行")
        print("   ⚠️ 缺少数据源完整性检查")
        print("   ⚠️ 缺少删除前的安全验证")
        
        # 8. 分析具体的风险概率
        print(f"\n📊 风险概率评估:")
        
        # 计算单一数据源记录比例
        total_records = only_ths_count + only_dcf_count
        cursor.execute('SELECT COUNT(*) FROM "个股人气表" WHERE "日期" = %s', (max_date,))
        all_records = cursor.fetchone()[0]
        
        if all_records > 0:
            single_source_ratio = total_records / all_records * 100
            print(f"   单一数据源记录比例: {single_source_ratio:.1f}%")
            
            if single_source_ratio > 30:
                print("   🔴 高风险: 单一数据源记录比例过高")
            elif single_source_ratio > 10:
                print("   🟡 中风险: 单一数据源记录比例适中")
            else:
                print("   🟢 低风险: 单一数据源记录比例较低")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    analyze_deletion_timing()
