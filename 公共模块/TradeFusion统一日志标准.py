#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion统一日志标准
整合所有日志系统，制定项目级统一规范
"""

import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

class TradeFusion统一日志器:
    """
    TradeFusion项目统一日志记录器
    
    设计原则：
    1. 三色标识：🔴模块名 🟡表名 🟢数字
    2. 精简输出：每模块一行核心信息
    3. 调用追踪：清晰的模块调用关系
    4. 性能优化：最小化日志开销
    """
    
    def __init__(self, module_name: str):
        self.module_name = module_name
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置优化的日志记录器"""
        logger = logging.getLogger(f'TradeFusion.{self.module_name}')
        logger.setLevel(logging.INFO)
        
        # 清除现有handlers避免重复
        logger.handlers.clear()
        
        # 确保logs目录存在
        Path('logs').mkdir(exist_ok=True)
        
        # 控制台输出（带颜色）
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter('[%(asctime)s] %(message)s', datefmt='%H:%M:%S')
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        # 文件存储（纯文本）
        file_handler = logging.FileHandler(
            f'logs/{self.module_name}.log',
            encoding='utf-8'
        )
        file_handler.setFormatter(console_formatter)
        logger.addHandler(file_handler)
        
        logger.propagate = False
        return logger
    
    # {{ AURA-X: Modify - 修正类型注解，使用Optional类型匹配None默认值. Approval: 寸止(ID:1737734400). }}
    def 记录模块执行(self, 操作描述: str, 数据量: Optional[int] = None, 调用方: Optional[str] = None):
        """
        记录模块执行结果（统一格式）
        
        Args:
            操作描述: 模块执行的操作描述
            数据量: 处理的数据量
            调用方: 调用此模块的上级模块
        """
        # 构建日志消息
        消息 = f"\033[91m[{self.module_name}]\033[0m {操作描述}"
        
        if 数据量 is not None:
            消息 += f" - \033[92m{数据量:,}条\033[0m"
        
        if 调用方:
            消息 += f" (由\033[91m[{调用方}]\033[0m调用)"
        
        self.logger.info(消息)
    
    def 记录数据库操作(self, 表操作列表: list, 调用方: Optional[str] = None):
        """
        记录数据库多表操作
        
        Args:
            表操作列表: [(表名, 数据量), ...] 格式的列表
            调用方: 调用此模块的上级模块
        """
        表信息 = "|".join([f"\033[93m{表名}\033[0m+\033[92m{数量:,}条\033[0m" 
                          for 表名, 数量 in 表操作列表])
        
        消息 = f"\033[91m[{self.module_name}]\033[0m 数据库导入完成 - {表信息}"
        
        if 调用方:
            消息 += f" (由\033[91m[{调用方}]\033[0m调用)"
        
        self.logger.info(消息)
    
    def 记录错误(self, 错误描述: str, 异常对象: Optional[Exception] = None):
        """记录错误信息"""
        消息 = f"❌ \033[91m[{self.module_name}]\033[0m {错误描述}"
        if 异常对象:
            消息 += f": {str(异常对象)}"
        self.logger.error(消息)

    def debug(self, 消息: str):
        """
        调试信息记录方法（兼容性方法）

        Args:
            消息: 调试消息内容
        """
        # 使用info级别输出调试信息，保持与统一日志标准一致
        self.logger.info(消息)

    def error(self, 消息: str):
        """
        错误信息记录方法（兼容性方法）

        Args:
            消息: 错误消息内容
        """
        # 使用统一的错误记录格式
        formatted_message = f"❌ \033[91m[{self.module_name}]\033[0m {消息}"
        self.logger.error(formatted_message)

    def info(self, 消息: str):
        """
        信息记录方法（兼容性方法）

        Args:
            消息: 信息消息内容
        """
        # 直接使用info级别输出，保持与统一日志标准一致
        self.logger.info(消息)

    def warning(self, 消息: str):
        """
        警告信息记录方法（兼容性方法）

        Args:
            消息: 警告消息内容
        """
        # 使用统一的警告记录格式
        formatted_message = f"⚠️ \033[93m[{self.module_name}]\033[0m {消息}"
        self.logger.warning(formatted_message)

    def warn(self, 消息: str):
        """
        警告信息记录方法（兼容性方法，warning的别名）

        Args:
            消息: 警告消息内容
        """
        self.warning(消息)


# 全局日志器缓存（性能优化）
_logger_cache: Dict[str, TradeFusion统一日志器] = {}

def 获取日志器(模块名: str) -> TradeFusion统一日志器:
    """获取模块日志器（带缓存优化）"""
    if 模块名 not in _logger_cache:
        _logger_cache[模块名] = TradeFusion统一日志器(模块名)
    return _logger_cache[模块名]


# 便捷函数
def 记录执行(模块名: str, 操作描述: str, 数据量: Optional[int] = None, 调用方: Optional[str] = None):
    """便捷的执行记录函数"""
    获取日志器(模块名).记录模块执行(操作描述, 数据量, 调用方)

def 记录数据库(模块名: str, 表操作列表: list, 调用方: Optional[str] = None):
    """便捷的数据库操作记录函数"""
    获取日志器(模块名).记录数据库操作(表操作列表, 调用方)

def 记录错误(模块名: str, 错误描述: str, 异常对象: Optional[Exception] = None):
    """便捷的错误记录函数"""
    获取日志器(模块名).记录错误(错误描述, 异常对象)


# 统一日志标准配置
日志标准配置 = {
    "格式规范": {
        "模块名颜色": "红色(\033[91m)",
        "表名颜色": "黄色(\033[93m)", 
        "数字颜色": "绿色(\033[92m)",
        "时间格式": "[HH:MM:SS]",
        "数字格式": "千分位分隔符"
    },
    "性能优化": {
        "日志器缓存": True,
        "异步写入": False,  # 简单项目暂不启用
        "批量写入": False,
        "文件轮转": "按大小50MB"
    },
    "存储策略": {
        "控制台输出": True,
        "文件存储": True,
        "日志保留": "30天",
        "压缩存储": False
    }
}


if __name__ == "__main__":
    # 使用示例
    logger = 获取日志器("测试模块")
    logger.记录模块执行("数据采集完成", 1234)
    logger.记录数据库操作([("个股解读表", 77), ("板块信息表", 12)], "选股宝清洗")
    logger.记录错误("网络连接失败")
