def process_stock_code_prefix(stock_code):
    """统一处理股票代码前缀
    
    参数:
        stock_code (str): 原始股票代码（不带交易所前缀）
    
    返回:
        str: 带交易所前缀的完整股票代码
    """
    if stock_code.startswith('6'):
        return f'SH{stock_code}'
    elif stock_code.startswith(('8', '9')):
        return f'BJ{stock_code}'
    else:
        return f'SZ{stock_code}'

def process_stock_list(stock_list):
    """批量处理股票代码列表
    
    参数:
        stock_list (list): 包含股票代码的列表，格式可以是字符串或元组 (股票代码, 其他数据...)
    
    返回:
        list: 处理后的股票代码列表，保持原始输入格式
    """
    processed_list = []
    for item in stock_list:
        if isinstance(item, tuple):
            code = item[0]
            processed_code = process_stock_code_prefix(code)
            processed_list.append( (processed_code,) + item[1:] )
        elif isinstance(item, str):
            processed_list.append( process_stock_code_prefix(item) )
        else:
            raise ValueError("不支持的输入类型，请输入字符串或元组")
    return processed_list
