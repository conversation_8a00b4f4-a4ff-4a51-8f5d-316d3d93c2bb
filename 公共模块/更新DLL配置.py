import pandas as pd
import codecs
import sys
import logging
import os
from datetime import datetime
import traceback

# 配置日志记录
def setup_logging():
    """设置日志配置"""
    log_filename = f"更新DLL配置_日志_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    log_path = os.path.join(os.path.dirname(__file__), log_filename)

    # 配置日志格式
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        handlers=[
            logging.FileHandler(log_path, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    logger = logging.getLogger(__name__)
    logger.info(f"日志文件已创建: {log_path}")
    return logger

# 初始化日志
logger = setup_logging()
logger.info("=" * 50)
logger.info("开始执行更新DLL配置脚本")
logger.info("=" * 50)

def check_file_exists(file_path, file_type="文件"):
    """检查文件是否存在"""
    logger.debug(f"检查{file_type}是否存在: {file_path}")
    if os.path.exists(file_path):
        logger.info(f"✓ {file_type}存在: {file_path}")
        return True
    else:
        logger.error(f"✗ {file_type}不存在: {file_path}")
        return False

def read_excel_file():
    """读取Excel文件"""
    excel_path = r'E:\助手\DDL配置.xlsx'
    logger.info(f"准备读取Excel文件: {excel_path}")

    try:
        # 检查文件是否存在
        if not check_file_exists(excel_path, "Excel文件"):
            raise FileNotFoundError(f"Excel文件不存在: {excel_path}")

        # 检查文件大小
        file_size = os.path.getsize(excel_path)
        logger.info(f"Excel文件大小: {file_size} 字节")

        # 读取Excel文件
        logger.debug("开始读取Excel文件...")
        df = pd.read_excel(excel_path, header=None)

        # 记录数据信息
        logger.info(f"Excel读取成功!")
        logger.info(f"数据形状: {df.shape} (行数: {df.shape[0]}, 列数: {df.shape[1]})")
        logger.debug(f"列索引: {list(df.columns)}")

        # 显示前几行数据（用于调试）
        logger.debug("Excel文件前5行数据:")
        for i, row in df.head().iterrows():
            logger.debug(f"  行{i}: {row.tolist()}")

        return df

    except ImportError as e:
        error_msg = "缺少必需的依赖包 'openpyxl'"
        logger.error(f"{error_msg}: {str(e)}")
        logger.error("请运行: pip install openpyxl")
        raise ImportError(error_msg)

    except FileNotFoundError as e:
        logger.error(f"文件未找到错误: {str(e)}")
        raise

    except pd.errors.EmptyDataError as e:
        error_msg = f"Excel文件为空或无法读取: {str(e)}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    except Exception as e:
        error_msg = f"读取Excel文件时发生未知错误: {str(e)}"
        logger.error(error_msg)
        logger.error(f"错误类型: {type(e).__name__}")
        logger.error(f"完整错误信息:\n{traceback.format_exc()}")
        raise

# 主程序开始
try:
    logger.info("步骤1: 读取Excel文件")
    df = read_excel_file()

except Exception as e:
    logger.critical(f"读取Excel文件失败，程序终止: {str(e)}")
    sys.exit(1)

def write_ini_files(df):
    """写入INI文件"""
    logger.info("步骤2: 开始写入INI文件")

    # 定义INI文件路径
    ini_paths = [
        r'E:\dzh2\KINGWA.ini',
        r'E:\dzh3\KINGWA.ini',
        r'E:\dzh4\KINGWA.ini'
    ]

    logger.info(f"需要处理的INI文件数量: {len(ini_paths)}")
    logger.info(f"Excel数据列数: {df.shape[1]}")

    # 检查列数是否匹配
    if df.shape[1] < len(ini_paths):
        logger.warning(f"Excel列数({df.shape[1]})少于INI文件数({len(ini_paths)})")
        logger.warning("部分INI文件可能不会被更新")
    elif df.shape[1] > len(ini_paths):
        logger.warning(f"Excel列数({df.shape[1]})多于INI文件数({len(ini_paths)})")
        logger.warning("部分Excel列数据将被忽略")

    success_count = 0
    error_count = 0

    # 遍历每一列并写入对应的INI文件
    for col_index, ini_path in enumerate(ini_paths):
        logger.info(f"处理第{col_index + 1}个INI文件: {ini_path}")

        try:
            # 检查列是否存在
            if col_index >= df.shape[1]:
                logger.warning(f"Excel中没有第{col_index + 1}列数据，跳过文件: {ini_path}")
                continue

            # 检查目标目录是否存在
            ini_dir = os.path.dirname(ini_path)
            if not os.path.exists(ini_dir):
                logger.error(f"目标目录不存在: {ini_dir}")
                error_count += 1
                continue

            # 备份原文件（如果存在）
            if os.path.exists(ini_path):
                backup_path = f"{ini_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                try:
                    import shutil
                    shutil.copy2(ini_path, backup_path)
                    logger.info(f"原文件已备份到: {backup_path}")
                except Exception as backup_error:
                    logger.warning(f"备份文件失败: {str(backup_error)}")

            # 获取当前列的数据
            column_data = df[col_index]
            valid_data = column_data.dropna()  # 移除空值

            logger.info(f"列{col_index + 1}数据统计:")
            logger.info(f"  总行数: {len(column_data)}")
            logger.info(f"  有效行数: {len(valid_data)}")
            logger.info(f"  空值行数: {len(column_data) - len(valid_data)}")

            if len(valid_data) == 0:
                logger.warning(f"列{col_index + 1}没有有效数据，将创建空文件")

            # 写入INI文件
            logger.debug(f"开始写入文件: {ini_path}")
            with codecs.open(ini_path, 'w', encoding='mbcs') as ini_file:  # 'mbcs'用于ANSI编码
                written_lines = 0
                for row_index, value in enumerate(column_data):
                    if pd.notna(value):  # 跳过空白行
                        try:
                            line_content = f"{value}\n"
                            ini_file.write(line_content)
                            written_lines += 1
                            logger.debug(f"  写入第{written_lines}行: {str(value)[:50]}{'...' if len(str(value)) > 50 else ''}")
                        except Exception as write_error:
                            logger.error(f"写入第{row_index + 1}行数据时出错: {str(write_error)}")
                            logger.error(f"问题数据: {repr(value)}")

                logger.info(f"✓ 文件写入完成: {ini_path}")
                logger.info(f"  实际写入行数: {written_lines}")
                success_count += 1

        except PermissionError as e:
            error_msg = f"权限错误，无法写入文件: {ini_path}"
            logger.error(f"{error_msg} - {str(e)}")
            logger.error("请检查文件是否被其他程序占用，或以管理员身份运行")
            error_count += 1

        except UnicodeEncodeError as e:
            error_msg = f"编码错误，无法写入文件: {ini_path}"
            logger.error(f"{error_msg} - {str(e)}")
            logger.error(f"问题字符位置: {e.start}-{e.end}")
            logger.error(f"问题字符: {repr(e.object[e.start:e.end])}")
            error_count += 1

        except Exception as e:
            error_msg = f"写入INI文件时发生未知错误: {ini_path}"
            logger.error(f"{error_msg} - {str(e)}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"完整错误信息:\n{traceback.format_exc()}")
            error_count += 1

    # 总结报告
    logger.info("=" * 30)
    logger.info("INI文件写入完成总结:")
    logger.info(f"  成功处理: {success_count} 个文件")
    logger.info(f"  失败处理: {error_count} 个文件")
    logger.info(f"  总计文件: {len(ini_paths)} 个文件")
    logger.info("=" * 30)

    return success_count, error_count

# 执行INI文件写入
try:
    success_count, error_count = write_ini_files(df)

    if error_count > 0:
        logger.warning(f"程序完成，但有 {error_count} 个文件处理失败")
        sys.exit(1)
    else:
        logger.info("✓ 所有文件处理成功！")

except Exception as e:
    logger.critical(f"写入INI文件过程中发生严重错误: {str(e)}")
    logger.critical(f"完整错误信息:\n{traceback.format_exc()}")
    sys.exit(1)

finally:
    logger.info("=" * 50)
    logger.info("脚本执行结束")
    logger.info("=" * 50)