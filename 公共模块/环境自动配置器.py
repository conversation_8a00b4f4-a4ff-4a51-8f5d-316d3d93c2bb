#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion环境自动配置器
功能：自动检测并配置最佳的Python环境
作者：TradeFusion团队
"""

import os
import sys
import subprocess
from pathlib import Path

def 获取项目根目录():
    """获取项目根目录"""
    return Path(__file__).parent.parent

def 检查虚拟环境():
    """检查本地虚拟环境是否存在且可用"""
    project_root = 获取项目根目录()
    venv_path = project_root / "venv"
    venv_python = venv_path / "Scripts" / "python.exe"
    
    if not venv_path.exists():
        return False, "虚拟环境目录不存在"
    
    if not venv_python.exists():
        return False, "虚拟环境Python解释器不存在"
    
    # 测试虚拟环境是否可用
    try:
        result = subprocess.run(
            [str(venv_python), "--version"],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            return True, f"虚拟环境可用: {result.stdout.strip()}"
        else:
            return False, f"虚拟环境测试失败: {result.stderr}"
    except Exception as e:
        return False, f"虚拟环境测试异常: {str(e)}"

def 创建虚拟环境():
    """创建本地虚拟环境"""
    project_root = 获取项目根目录()
    venv_path = project_root / "venv"
    
    print("🔧 正在创建本地虚拟环境...")
    print(f"目标路径: {venv_path}")
    
    try:
        # 创建虚拟环境
        result = subprocess.run(
            [sys.executable, "-m", "venv", str(venv_path)],
            capture_output=True,
            text=True,
            cwd=str(project_root)
        )
        
        if result.returncode == 0:
            print("✅ 虚拟环境创建成功")
            return True
        else:
            print(f"❌ 虚拟环境创建失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 虚拟环境创建异常: {str(e)}")
        return False

def 安装依赖():
    """在虚拟环境中安装项目依赖"""
    project_root = 获取项目根目录()
    venv_python = project_root / "venv" / "Scripts" / "python.exe"
    requirements_file = project_root / "requirements.txt"
    
    if not requirements_file.exists():
        print("⚠️ requirements.txt 文件不存在，跳过依赖安装")
        return True
    
    print("📦 正在安装项目依赖...")
    
    try:
        # 升级pip
        subprocess.run(
            [str(venv_python), "-m", "pip", "install", "--upgrade", "pip"],
            capture_output=True,
            text=True
        )
        
        # 安装依赖
        result = subprocess.run(
            [str(venv_python), "-m", "pip", "install", "-r", str(requirements_file)],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ 依赖安装成功")
            return True
        else:
            print(f"❌ 依赖安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 依赖安装异常: {str(e)}")
        return False

def 配置IDE环境():
    """配置IDE环境设置"""
    project_root = 获取项目根目录()
    venv_python = project_root / "venv" / "Scripts" / "python.exe"
    
    print("🔧 配置IDE环境...")
    
    # 创建.vscode目录（如果不存在）
    vscode_dir = project_root / ".vscode"
    vscode_dir.mkdir(exist_ok=True)
    
    # 创建settings.json
    settings_content = f'''{{
    "python.defaultInterpreterPath": "{str(venv_python).replace(chr(92), '/')}"
}}'''
    
    settings_file = vscode_dir / "settings.json"
    try:
        with open(settings_file, 'w', encoding='utf-8') as f:
            f.write(settings_content)
        print(f"✅ VS Code配置已更新: {settings_file}")
    except Exception as e:
        print(f"⚠️ VS Code配置更新失败: {str(e)}")
    
    return True

def 验证环境():
    """验证环境配置是否正确"""
    project_root = 获取项目根目录()
    venv_python = project_root / "venv" / "Scripts" / "python.exe"
    
    print("🔍 验证环境配置...")
    
    # 检查Python版本
    try:
        result = subprocess.run(
            [str(venv_python), "--version"],
            capture_output=True,
            text=True
        )
        print(f"✅ Python版本: {result.stdout.strip()}")
    except Exception as e:
        print(f"❌ Python版本检查失败: {str(e)}")
        return False
    
    # 检查关键依赖
    dependencies = ["playwright", "pandas", "numpy"]
    for dep in dependencies:
        try:
            result = subprocess.run(
                [str(venv_python), "-c", f"import {dep}; print(f'{dep} 可用')"],
                capture_output=True,
                text=True
            )
            if result.returncode == 0:
                print(f"✅ {dep} 已安装")
            else:
                print(f"❌ {dep} 不可用")
        except Exception as e:
            print(f"❌ {dep} 检查失败: {str(e)}")
    
    return True

def 强制激活虚拟环境():
    """强制激活虚拟环境 - 确保在按F5时能够激活"""
    # {{ AURA-X: Add - 检查环境变量，避免启动器调用时的双进程问题. Approval: 寸止(ID:1738320024). }}
    if os.environ.get('TRADEFUSION_SKIP_ENV_CHECK') == '1':
        print("🔄 检测到启动器环境，跳过虚拟环境检查")
        return True

    project_root = 获取项目根目录()
    venv_python = project_root / "venv" / "Scripts" / "python.exe"

    print("🔄 强制激活虚拟环境...")
    print(f"当前Python: {sys.executable}")
    print(f"目标虚拟环境: {venv_python}")

    # 检查当前是否已经在虚拟环境中
    current_python = Path(sys.executable)
    if current_python.samefile(venv_python) if venv_python.exists() else False:
        print("✅ 虚拟环境已激活！")
        return True

    # 如果不在虚拟环境中，强制重新启动使用虚拟环境
    if venv_python.exists():
        print(f"🔄 强制切换到虚拟环境...")
        try:
            # 使用虚拟环境的Python重新运行当前脚本
            import subprocess

            # 获取当前脚本的完整参数
            script_args = [str(venv_python), __file__] + sys.argv[1:]

            print(f"执行命令: {' '.join(script_args)}")

            # 在新进程中运行
            result = subprocess.run(
                script_args,
                cwd=str(project_root),
                capture_output=False,  # 直接显示输出
                text=True
            )

            # 运行完成后退出当前进程
            print(f"\n🎯 虚拟环境执行完成，返回码: {result.returncode}")
            input("按Enter键退出...")
            sys.exit(result.returncode)

        except Exception as e:
            print(f"❌ 虚拟环境激活失败: {str(e)}")
            print("继续使用当前环境...")
            return False
    else:
        print("❌ 虚拟环境不存在，将创建新的虚拟环境")
        return False

def main():
    """主函数 - 按F5运行时强制激活虚拟环境"""
    print("🚀 TradeFusion环境自动配置器")
    print("=" * 50)

    try:
        # 步骤0: 强制检查并激活虚拟环境
        project_root = 获取项目根目录()
        venv_python = project_root / "venv" / "Scripts" / "python.exe"
        current_python = Path(sys.executable)

        # 强制激活虚拟环境（无论当前状态如何）
        if not (venv_python.exists() and current_python.samefile(venv_python)):
            print("⚠️ 当前未使用项目虚拟环境，强制激活...")

            # 如果虚拟环境不存在，先创建
            if not venv_python.exists():
                print("🔧 虚拟环境不存在，正在创建...")
                if not 创建虚拟环境():
                    print("❌ 虚拟环境创建失败")
                    input("按Enter键退出...")
                    return False

                # 安装依赖
                if not 安装依赖():
                    print("⚠️ 依赖安装失败，但虚拟环境已创建")

            # 强制激活虚拟环境
            强制激活虚拟环境()
            return True  # 激活后会重新运行，这里直接返回

        # 如果已经在虚拟环境中，继续执行配置
        print("✅ 当前已在虚拟环境中运行")

        # 步骤1: 检查虚拟环境状态
        is_available, message = 检查虚拟环境()
        print(f"🔍 检查虚拟环境: {message}")

        # 步骤2: 配置IDE
        配置IDE环境()

        # 步骤3: 验证环境
        验证环境()

        print("\n" + "=" * 50)
        print("🎉 环境配置完成！")
        print("💡 当前状态:")
        print(f"  ✅ 虚拟环境已激活: {sys.executable}")
        print("  ✅ IDE配置已更新")
        print("  ✅ 所有依赖已验证")
        print("  ✅ 可以正常运行TradeFusion项目")
        print("=" * 50)

        # 在PyCharm中运行时暂停，方便查看结果
        input("\n按Enter键退出...")
        return True

    except Exception as e:
        print(f"❌ 环境配置失败: {str(e)}")
        import traceback
        traceback.print_exc()
        input("按Enter键退出...")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("\n按Enter键退出...")
        sys.exit(1)
