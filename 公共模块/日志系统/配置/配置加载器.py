#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion日志系统配置加载器
统一管理日志配置的加载和访问
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional

class 日志配置加载器:
    """日志配置加载器"""
    
    def __init__(self):
        self.配置文件路径 = Path(__file__).parent / "logging.yaml"
        self.配置数据: Optional[Dict[str, Any]] = None
        self._加载配置()
    
    def _加载配置(self):
        """加载配置文件"""
        try:
            if self.配置文件路径.exists():
                with open(self.配置文件路径, 'r', encoding='utf-8') as f:
                    self.配置数据 = yaml.safe_load(f)
            else:
                # 使用默认配置
                self.配置数据 = self._获取默认配置()
                print(f"⚠️ 配置文件不存在，使用默认配置: {self.配置文件路径}")
        except Exception as e:
            print(f"❌ 加载日志配置失败: {e}")
            self.配置数据 = self._获取默认配置()
    
    def _获取默认配置(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "logging": {
                "level": "INFO",
                "format": "[%(asctime)s] %(levelname)s - %(message)s",
                "datefmt": "%H:%M:%S",
                "file": {
                    "enabled": True,
                    "path": "logs/",
                    "max_size_mb": 50,
                    "backup_count": 30,
                    "encoding": "utf-8"
                },
                "console": {
                    "enabled": True,
                    "colored": True
                }
            },
            "colors": {
                "module": "\033[91m",
                "table": "\033[93m",
                "number": "\033[92m",
                "reset": "\033[0m",
                "error": "❌"
            },
            "performance": {
                "logger_cache": True,
                "async_write": False,
                "batch_write": False
            }
        }
    
    def 获取配置(self, 键路径: str, 默认值: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            键路径: 配置键路径，如 'logging.level' 或 'colors.module'
            默认值: 如果配置不存在时返回的默认值
            
        Returns:
            配置值
        """
        if not self.配置数据:
            return 默认值
        
        keys = 键路径.split('.')
        current = self.配置数据
        
        try:
            for key in keys:
                current = current[key]
            return current
        except (KeyError, TypeError):
            return 默认值
    
    def 获取日志级别(self) -> str:
        """获取日志级别"""
        return self.获取配置("logging.level", "INFO")
    
    def 获取日志格式(self) -> str:
        """获取日志格式"""
        return self.获取配置("logging.format", "[%(asctime)s] %(levelname)s - %(message)s")
    
    def 获取时间格式(self) -> str:
        """获取时间格式"""
        return self.获取配置("logging.datefmt", "%H:%M:%S")
    
    def 获取文件配置(self) -> Dict[str, Any]:
        """获取文件配置"""
        return self.获取配置("logging.file", {
            "enabled": True,
            "path": "logs/",
            "max_size_mb": 50,
            "backup_count": 30,
            "encoding": "utf-8"
        })
    
    def 获取颜色配置(self) -> Dict[str, str]:
        """获取颜色配置"""
        return self.获取配置("colors", {
            "module": "\033[91m",
            "table": "\033[93m", 
            "number": "\033[92m",
            "reset": "\033[0m",
            "error": "❌"
        })
    
    def 获取性能配置(self) -> Dict[str, Any]:
        """获取性能配置"""
        return self.获取配置("performance", {
            "logger_cache": True,
            "async_write": False,
            "batch_write": False
        })
    
    def 是否启用文件日志(self) -> bool:
        """是否启用文件日志"""
        return self.获取配置("logging.file.enabled", True)
    
    def 是否启用控制台日志(self) -> bool:
        """是否启用控制台日志"""
        return self.获取配置("logging.console.enabled", True)
    
    def 是否启用颜色(self) -> bool:
        """是否启用颜色"""
        return self.获取配置("logging.console.colored", True)
    
    def 重新加载配置(self):
        """重新加载配置"""
        self._加载配置()
        print("🔄 日志配置已重新加载")


# 全局配置实例
_配置实例: Optional[日志配置加载器] = None

def 获取配置实例() -> 日志配置加载器:
    """获取全局配置实例"""
    global _配置实例
    if _配置实例 is None:
        _配置实例 = 日志配置加载器()
    return _配置实例

def 获取日志配置(键路径: str, 默认值: Any = None) -> Any:
    """便捷函数：获取日志配置"""
    return 获取配置实例().获取配置(键路径, 默认值)


if __name__ == "__main__":
    # 测试配置加载器
    loader = 日志配置加载器()
    
    print("📋 日志配置测试:")
    print(f"日志级别: {loader.获取日志级别()}")
    print(f"日志格式: {loader.获取日志格式()}")
    print(f"文件日志: {loader.是否启用文件日志()}")
    print(f"控制台日志: {loader.是否启用控制台日志()}")
    print(f"颜色支持: {loader.是否启用颜色()}")
    
    colors = loader.获取颜色配置()
    print(f"颜色配置: {colors}")
    
    print("✅ 配置加载器测试完成")
