#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion日志系统性能分析器
从框架师角度分析日志系统的性能和资源占用
"""

import time
import psutil
import threading
import os
from pathlib import Path
from typing import Dict, List
import logging

class 日志性能分析器:
    """日志系统性能分析器"""
    
    def __init__(self):
        self.开始时间 = time.time()
        self.内存基线 = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        self.CPU基线 = psutil.cpu_percent()
        self.性能数据 = {
            "日志写入次数": 0,
            "总写入字节": 0,
            "平均写入时间": 0,
            "最大内存占用": 0,
            "CPU峰值": 0
        }
    
    def 分析日志文件大小(self) -> Dict:
        """分析日志文件占用情况"""
        logs_dir = Path("logs")
        if not logs_dir.exists():
            return {"总大小": 0, "文件数量": 0, "平均大小": 0}
        
        总大小 = 0
        文件列表 = []
        
        for log_file in logs_dir.glob("*.log"):
            大小 = log_file.stat().st_size
            总大小 += 大小
            文件列表.append({
                "文件名": log_file.name,
                "大小MB": round(大小 / 1024 / 1024, 2),
                "修改时间": log_file.stat().st_mtime
            })
        
        return {
            "总大小MB": round(总大小 / 1024 / 1024, 2),
            "文件数量": len(文件列表),
            "平均大小MB": round(总大小 / len(文件列表) / 1024 / 1024, 2) if 文件列表 else 0,
            "文件详情": sorted(文件列表, key=lambda x: x["大小MB"], reverse=True)
        }
    
    def 测试日志写入性能(self, 测试次数: int = 1000) -> Dict:
        """测试日志写入性能"""
        from TradeFusion统一日志标准 import 获取日志器
        
        logger = 获取日志器("性能测试")
        
        开始时间 = time.time()
        开始内存 = psutil.Process().memory_info().rss / 1024 / 1024
        
        # 执行日志写入测试
        for i in range(测试次数):
            logger.记录模块执行(f"性能测试第{i+1}次", i * 100, "测试调用方")
            
            # 每100次检查一次资源占用
            if i % 100 == 0:
                当前内存 = psutil.Process().memory_info().rss / 1024 / 1024
                当前CPU = psutil.cpu_percent()

                # {{ AURA-X: Modify - 修复类型转换问题，将float转换为int. Approval: 寸止(ID:1737734400). }}
                self.性能数据["最大内存占用"] = max(self.性能数据["最大内存占用"], 当前内存 - 开始内存)
                self.性能数据["CPU峰值"] = max(self.性能数据["CPU峰值"], int(当前CPU))
        
        结束时间 = time.time()
        结束内存 = psutil.Process().memory_info().rss / 1024 / 1024
        
        总耗时 = 结束时间 - 开始时间
        内存增长 = 结束内存 - 开始内存
        平均写入时间 = (总耗时 / 测试次数) * 1000  # 毫秒
        
        return {
            "测试次数": 测试次数,
            "总耗时秒": round(总耗时, 3),
            "平均写入时间毫秒": round(平均写入时间, 3),
            "内存增长MB": round(内存增长, 2),
            "每秒写入能力": round(测试次数 / 总耗时, 0),
            "性能评级": self._评估性能等级(平均写入时间, 内存增长)
        }
    
    def _评估性能等级(self, 平均写入时间: float, 内存增长: float) -> str:
        """评估性能等级"""
        if 平均写入时间 < 0.1 and 内存增长 < 1:
            return "🟢 优秀"
        elif 平均写入时间 < 0.5 and 内存增长 < 5:
            return "🟡 良好"
        elif 平均写入时间 < 1.0 and 内存增长 < 10:
            return "🟠 一般"
        else:
            return "🔴 需要优化"
    
    def 分析并发性能(self, 线程数: int = 5, 每线程写入: int = 200) -> Dict:
        """分析并发日志写入性能"""
        from TradeFusion统一日志标准 import 获取日志器
        
        开始时间 = time.time()
        开始内存 = psutil.Process().memory_info().rss / 1024 / 1024
        
        def 线程任务(线程ID: int):
            logger = 获取日志器(f"并发测试{线程ID}")
            for i in range(每线程写入):
                logger.记录模块执行(f"并发测试线程{线程ID}第{i+1}次", i * 10)
        
        # 创建并启动线程
        线程列表 = []
        for i in range(线程数):
            线程 = threading.Thread(target=线程任务, args=(i,))
            线程列表.append(线程)
            线程.start()
        
        # 等待所有线程完成
        for 线程 in 线程列表:
            线程.join()
        
        结束时间 = time.time()
        结束内存 = psutil.Process().memory_info().rss / 1024 / 1024
        
        总耗时 = 结束时间 - 开始时间
        总写入次数 = 线程数 * 每线程写入
        内存增长 = 结束内存 - 开始内存
        
        return {
            "线程数": 线程数,
            "总写入次数": 总写入次数,
            "总耗时秒": round(总耗时, 3),
            "并发写入速度": round(总写入次数 / 总耗时, 0),
            "内存增长MB": round(内存增长, 2),
            "并发性能评级": self._评估并发性能(总写入次数 / 总耗时, 内存增长)
        }
    
    def _评估并发性能(self, 写入速度: float, 内存增长: float) -> str:
        """评估并发性能等级"""
        if 写入速度 > 1000 and 内存增长 < 5:
            return "🟢 优秀"
        elif 写入速度 > 500 and 内存增长 < 10:
            return "🟡 良好"
        elif 写入速度 > 200 and 内存增长 < 20:
            return "🟠 一般"
        else:
            return "🔴 需要优化"
    
    def 生成性能报告(self) -> Dict:
        """生成完整的性能分析报告"""
        print("🔧 开始TradeFusion日志系统性能分析...")
        
        # 1. 文件大小分析
        print("📁 分析日志文件占用...")
        文件分析 = self.分析日志文件大小()
        
        # 2. 写入性能测试
        print("⚡ 测试日志写入性能...")
        写入性能 = self.测试日志写入性能(1000)
        
        # 3. 并发性能测试
        print("🔄 测试并发写入性能...")
        并发性能 = self.分析并发性能(5, 200)
        
        # 4. 系统资源占用
        当前内存 = psutil.Process().memory_info().rss / 1024 / 1024
        当前CPU = psutil.cpu_percent()
        
        报告 = {
            "分析时间": time.strftime("%Y-%m-%d %H:%M:%S"),
            "文件占用分析": 文件分析,
            "写入性能测试": 写入性能,
            "并发性能测试": 并发性能,
            "系统资源": {
                "当前内存占用MB": round(当前内存, 2),
                "当前CPU使用率": f"{当前CPU}%",
                "内存基线MB": round(self.内存基线, 2),
                "内存增长MB": round(当前内存 - self.内存基线, 2)
            },
            "优化建议": self._生成优化建议(文件分析, 写入性能, 并发性能)
        }
        
        return 报告
    
    def _生成优化建议(self, 文件分析: Dict, 写入性能: Dict, 并发性能: Dict) -> List[str]:
        """生成优化建议"""
        建议列表 = []
        
        # 文件大小建议
        if 文件分析["总大小MB"] > 100:
            建议列表.append("📁 日志文件总大小超过100MB，建议启用日志轮转")
        
        # 写入性能建议
        if 写入性能["平均写入时间毫秒"] > 1.0:
            建议列表.append("⚡ 日志写入时间较长，建议启用异步写入")
        
        # 内存占用建议
        if 写入性能["内存增长MB"] > 10:
            建议列表.append("💾 内存占用增长较大，建议优化日志器缓存策略")
        
        # 并发性能建议
        if 并发性能["并发写入速度"] < 500:
            建议列表.append("🔄 并发写入性能较低，建议使用线程安全的日志器")
        
        # 通用建议
        建议列表.extend([
            "🎯 建议使用统一日志标准，避免多套日志系统",
            "🗂️ 建议定期清理旧日志文件，节省磁盘空间",
            "📊 建议在生产环境启用日志压缩和备份"
        ])
        
        return 建议列表


def main():
    """主函数"""
    分析器 = 日志性能分析器()
    报告 = 分析器.生成性能报告()
    
    print("\n" + "=" * 80)
    print("📊 TradeFusion日志系统性能分析报告")
    print("=" * 80)
    
    print(f"\n📁 文件占用分析:")
    print(f"   总大小: {报告['文件占用分析']['总大小MB']} MB")
    print(f"   文件数量: {报告['文件占用分析']['文件数量']} 个")
    
    print(f"\n⚡ 写入性能测试:")
    print(f"   平均写入时间: {报告['写入性能测试']['平均写入时间毫秒']} 毫秒")
    print(f"   每秒写入能力: {报告['写入性能测试']['每秒写入能力']} 次")
    print(f"   性能评级: {报告['写入性能测试']['性能评级']}")
    
    print(f"\n🔄 并发性能测试:")
    print(f"   并发写入速度: {报告['并发性能测试']['并发写入速度']} 次/秒")
    print(f"   并发性能评级: {报告['并发性能测试']['并发性能评级']}")
    
    print(f"\n💾 系统资源:")
    print(f"   内存增长: {报告['系统资源']['内存增长MB']} MB")
    print(f"   CPU使用率: {报告['系统资源']['当前CPU使用率']}")
    
    print(f"\n💡 优化建议:")
    for i, 建议 in enumerate(报告['优化建议'], 1):
        print(f"   {i}. {建议}")
    
    print("\n" + "=" * 80)
    print("🎉 性能分析完成！")

if __name__ == "__main__":
    main()
