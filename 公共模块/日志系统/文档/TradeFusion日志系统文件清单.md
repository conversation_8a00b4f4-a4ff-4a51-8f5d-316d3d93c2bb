# TradeFusion日志系统文件清单

## 📋 日志系统相关文件总览

### 🔧 核心日志模块

#### 1. 统一日志标准模块
- **文件路径**: `公共模块/TradeFusion统一日志标准.py`
- **功能**: TradeFusion项目统一日志记录器
- **特点**: 三色标识系统（🔴模块名 🟡表名 🟢数字）
- **状态**: ✅ 当前使用

#### 2. 日志性能分析器
- **文件路径**: `公共模块/日志系统性能分析器.py`
- **功能**: 日志系统性能分析和优化
- **状态**: ✅ 当前使用

#### 3. 日志监控器
- **文件路径**: `日志监控器.py`
- **功能**: 实时监控logs目录下所有.log文件的变化
- **特点**: 实时显示新增内容，支持多文件监控
- **状态**: ✅ 当前使用

### 📁 日志配置文件

#### 1. 数据源配置
- **文件路径**: `config/data_sources.yaml`
- **日志配置段**: 
  ```yaml
  logging:
    level: "INFO"
    format: "[%(asctime)s] %(levelname)s - %(message)s"
    file:
      enabled: true
      path: "logs/data_collection.log"
      max_size_mb: 10
      backup_count: 5
  ```

### 📂 日志存储目录

#### logs/ 目录结构
```
logs/
├── startup.log                           # 系统启动日志
├── 个股人气表.log                         # 个股人气表模块日志
├── 个股人气表.log.2025-07-22              # 个股人气表历史日志
├── 个股接力表.log                         # 个股接力表模块日志
├── 个股解读_板块信息_关联表.log            # 个股解读模块日志
├── 个股解读_板块信息_关联表.log.2025-07-22 # 个股解读历史日志
├── 人气_东财采集.log                      # 东财人气采集日志
├── 人气_同花采集.log                      # 同花人气采集日志
├── 所属板块强度.log                       # 所属板块强度日志
├── 所属板块评分表.log                     # 所属板块评分表日志
├── 数据库结构报告生成器.log               # 数据库结构报告生成器日志
├── 板块涨停表.log                         # 板块涨停表日志
├── 板块涨停表.log.2025-07-22              # 板块涨停表历史日志
├── 板块精选.log                           # 板块精选日志
├── 板块精选.log.2025-07-22                # 板块精选历史日志
├── 生成DZH3人气板块.log                   # DZH3人气板块生成日志
├── 综合人气190.log                        # 综合人气190日志
├── 选股宝_大智慧str.log                   # 选股宝STR文件生成日志
├── 选股宝抓取.log                         # 选股宝抓取日志
├── 选股宝抓取.log.2025-07-22              # 选股宝抓取历史日志
├── 选股宝清洗.log                         # 选股宝清洗日志
├── 选股宝清洗.log.2025-07-22              # 选股宝清洗历史日志
├── 采集_本地数据.log                      # 本地数据采集日志
└── 采集_本地数据.log.2025-07-22           # 本地数据采集历史日志
```

### 🗂️ 备份文件

#### 备份目录中的日志文件
- `备份_20250722_105334/公共模块/TradeFusion统一日志标准.py`
- `备份_20250722_104731/公共模块/TradeFusion统一日志标准.py`

### 📊 日志系统统计

#### 文件数量统计
- **核心日志模块**: 3个文件
- **配置文件**: 1个文件（包含日志配置段）
- **当前日志文件**: 23个活跃日志文件
- **历史日志文件**: 8个历史日志文件
- **备份文件**: 2个备份文件

#### 模块覆盖情况
- ✅ **数据采集层**: 采集_本地数据、人气_东财采集、人气_同花采集、选股宝抓取、选股宝清洗
- ✅ **数据处理层**: 个股解读_板块信息_关联表、板块涨停表、板块精选、个股接力表、个股人气表
- ✅ **输出层**: 综合人气190、生成DZH3人气板块、选股宝_大智慧str、所属板块强度、所属板块评分表
- ✅ **系统层**: startup、数据库结构报告生成器

### 🎯 日志标准规范

#### 三色标识系统
- 🔴 **红色**: 模块名称 `\033[91m[模块名]\033[0m`
- 🟡 **黄色**: 数据库表名 `\033[93m表名\033[0m`
- 🟢 **绿色**: 数字数据 `\033[92m数量\033[0m`

#### 日志格式模板
```
[HH:MM:SS] 🔴[模块名] 操作描述 - 🟢数量条 (由🔴[调用方]调用)
```

#### 性能优化特性
- ✅ 日志器缓存机制
- ✅ 双重输出（控制台+文件）
- ✅ UTF-8编码支持
- ✅ 千分位数字格式化
- ✅ 文件轮转（50MB限制）

### 📈 使用状态

#### 当前状态
- **统一日志标准**: ✅ 已部署并正常使用
- **日志监控**: ✅ 实时监控功能正常
- **性能分析**: ✅ 性能分析器可用
- **文件轮转**: ✅ 自动轮转正常工作
- **历史备份**: ✅ 历史日志保留完整

#### 覆盖率
- **模块覆盖率**: 100% (所有业务模块都有对应日志文件)
- **标准化率**: 100% (所有模块都使用统一日志标准)
- **监控覆盖率**: 100% (所有日志文件都被监控器覆盖)

---

**生成时间**: 2025-07-23  
**文档版本**: v1.0  
**维护状态**: 当前使用，定期更新
