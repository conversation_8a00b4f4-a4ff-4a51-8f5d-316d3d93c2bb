#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion 实时日志监控器
监控logs目录下所有.log文件的变化，实时显示新增内容
"""

import os
import time
import glob
from pathlib import Path
from datetime import datetime

class LogMonitor:
    def __init__(self, log_dir="logs"):
        self.log_dir = Path(log_dir)
        self.file_positions = {}  # 记录每个文件的读取位置
        
    def get_log_files(self):
        """获取所有.log文件"""
        return list(self.log_dir.glob("*.log"))
    
    def read_new_content(self, file_path):
        """读取文件的新增内容"""
        try:
            if not file_path.exists():
                return ""
            
            # 获取文件当前大小
            current_size = file_path.stat().st_size
            
            # 获取上次读取位置
            last_position = self.file_positions.get(str(file_path), 0)
            
            # 如果文件变小了（可能被重新创建），重置位置
            if current_size < last_position:
                last_position = 0
            
            # 如果没有新内容，返回空
            if current_size == last_position:
                return ""
            
            # 读取新内容
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(last_position)
                new_content = f.read()
                
                # 更新位置
                self.file_positions[str(file_path)] = f.tell()
                
                return new_content
                
        except Exception as e:
            return f"[ERROR] 读取文件 {file_path} 失败: {e}\n"
    
    def format_log_line(self, file_name, line):
        """格式化日志行"""
        if not line.strip():
            return ""
        
        timestamp = datetime.now().strftime("%H:%M:%S")
        # 使用不同颜色区分不同模块
        colors = {
            "人气_东财采集.log": "\033[94m",  # 蓝色
            "人气_同花采集.log": "\033[95m",  # 紫色
            "选股宝抓取.log": "\033[92m",     # 绿色
            "采集_本地数据.log": "\033[93m",  # 黄色
            "选股宝清洗.log": "\033[96m",     # 青色
            "个股解读_板块信息_关联表.log": "\033[91m",  # 红色
        }
        
        color = colors.get(file_name, "\033[97m")  # 默认白色
        reset = "\033[0m"
        
        return f"[{timestamp}] {color}[{file_name[:-4]}]{reset} {line}"
    
    def monitor(self):
        """开始监控"""
        print("🔍 TradeFusion 实时日志监控器启动")
        print("=" * 60)
        print("监控目录:", self.log_dir.absolute())
        print("按 Ctrl+C 停止监控")
        print("=" * 60)
        
        try:
            while True:
                # 获取当前所有日志文件
                log_files = self.get_log_files()
                
                # 检查每个文件的新内容
                for log_file in log_files:
                    new_content = self.read_new_content(log_file)
                    if new_content:
                        # 按行处理新内容
                        for line in new_content.split('\n'):
                            if line.strip():
                                formatted_line = self.format_log_line(log_file.name, line)
                                if formatted_line:
                                    print(formatted_line)
                
                # 短暂休眠
                time.sleep(0.5)
                
        except KeyboardInterrupt:
            print("\n\n🛑 日志监控已停止")
        except Exception as e:
            print(f"\n❌ 监控出错: {e}")

if __name__ == "__main__":
    monitor = LogMonitor()
    monitor.monitor()
