#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志性能分析器启动脚本
提供便捷的性能分析器启动方式
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入并启动性能分析器
if __name__ == "__main__":
    # 切换到项目根目录
    os.chdir(project_root)
    
    # 导入性能分析器
    from 公共模块.日志系统.日志系统性能分析器 import 日志性能分析器
    
    print("🚀 启动TradeFusion日志性能分析器...")
    print(f"📁 项目根目录: {project_root}")
    print("=" * 60)
    
    # 创建并启动分析器
    analyzer = 日志性能分析器()
    
    print("📊 性能分析器已启动，可以开始监控日志系统性能")
    print("💡 使用方法：")
    print("   analyzer.开始监控()  # 开始性能监控")
    print("   analyzer.生成报告()  # 生成性能报告")
    print("   analyzer.停止监控()  # 停止监控")
    
    # 进入交互模式
    import code
    code.interact(local=locals())
